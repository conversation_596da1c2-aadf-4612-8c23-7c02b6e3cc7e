{% extends "base.html" %} {% block title %}登录 - 邮件客户端{% endblock %} {%
block content %}
<div class="row justify-content-center">
  <div class="col-md-6">
    <div class="card">
      <div class="card-body p-5">
        <div class="text-center mb-4">
          <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
          <h2 class="card-title">邮件客户端登录</h2>
          <p class="text-muted">基于CLI底层的简化版本</p>
        </div>

        <form method="POST">
          <div class="mb-3">
            <label for="email" class="form-label">
              <i class="fas fa-at"></i> 邮箱地址
            </label>
            <input
              type="email"
              class="form-control"
              id="email"
              name="email"
              placeholder="输入您已配置的邮箱地址"
              required
            />
          </div>

          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary btn-lg">
              <i class="fas fa-sign-in-alt"></i> 登录
            </button>
          </div>
        </form>

        <hr class="my-4" />

        <div class="text-center">
          <p class="text-muted">没有配置过邮箱？</p>
          <a
            href="{{ url_for('add_account') }}"
            class="btn btn-outline-primary"
          >
            <i class="fas fa-plus"></i> 添加邮箱账户
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
