#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中邮件的撤回状态
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent))

from common.utils import setup_logging
from server.new_db_handler import EmailService

# 设置日志
logger = setup_logging("check_recall_status")

def check_recall_status():
    """检查数据库中邮件的撤回状态"""
    print("=" * 60)
    print("🔍 检查邮件撤回状态")
    print("=" * 60)
    
    # 初始化邮件服务
    email_service = EmailService()
    
    # 检查用户邮箱
    user_email = "<EMAIL>"
    
    print(f"📧 检查用户: {user_email}")
    
    # 1. 检查收件箱邮件（包含已撤回）
    print("\n1️⃣ 检查收件箱邮件（包含已撤回）...")
    received_emails_all = email_service.list_emails(
        user_email=user_email,
        include_recalled=True,  # 包含已撤回邮件
        limit=10
    )
    
    print(f"📥 收件箱总邮件数: {len(received_emails_all)}")
    
    recalled_count = 0
    for i, email in enumerate(received_emails_all):
        is_recalled = email.get("is_recalled", False)
        if is_recalled:
            recalled_count += 1
        
        status = "🔙已撤回" if is_recalled else ("✅已读" if email.get("is_read") else "📬未读")
        subject = email.get("subject", "(无主题)")[:30]
        from_addr = email.get("from_addr", "")[:20]
        
        print(f"  {i+1:2d}. {status:<8} {from_addr:<20} {subject}")
    
    print(f"📊 已撤回邮件数量: {recalled_count}")
    
    # 2. 检查收件箱邮件（不包含已撤回）
    print("\n2️⃣ 检查收件箱邮件（不包含已撤回）...")
    received_emails_filtered = email_service.list_emails(
        user_email=user_email,
        include_recalled=False,  # 不包含已撤回邮件
        limit=10
    )
    
    print(f"📥 过滤后收件箱邮件数: {len(received_emails_filtered)}")
    
    for i, email in enumerate(received_emails_filtered):
        is_recalled = email.get("is_recalled", False)
        status = "🔙已撤回" if is_recalled else ("✅已读" if email.get("is_read") else "📬未读")
        subject = email.get("subject", "(无主题)")[:30]
        from_addr = email.get("from_addr", "")[:20]
        
        print(f"  {i+1:2d}. {status:<8} {from_addr:<20} {subject}")
    
    # 3. 检查已发送邮件（包含已撤回）
    print("\n3️⃣ 检查已发送邮件（包含已撤回）...")
    sent_emails_all = email_service.list_sent_emails(
        from_addr=user_email,
        include_recalled=True,  # 包含已撤回邮件
        limit=10
    )
    
    print(f"📤 已发送总邮件数: {len(sent_emails_all)}")
    
    sent_recalled_count = 0
    for i, email in enumerate(sent_emails_all):
        is_recalled = email.get("is_recalled", False)
        if is_recalled:
            sent_recalled_count += 1
        
        status = "🔙已撤回" if is_recalled else ("✅已读" if email.get("is_read") else "📬未读")
        subject = email.get("subject", "(无主题)")[:30]
        to_addrs = str(email.get("to_addrs", []))[:20]
        
        print(f"  {i+1:2d}. {status:<8} {to_addrs:<20} {subject}")
    
    print(f"📊 已撤回的已发送邮件数量: {sent_recalled_count}")
    
    # 4. 检查已发送邮件（不包含已撤回）
    print("\n4️⃣ 检查已发送邮件（不包含已撤回）...")
    sent_emails_filtered = email_service.list_sent_emails(
        from_addr=user_email,
        include_recalled=False,  # 不包含已撤回邮件
        limit=10
    )
    
    print(f"📤 过滤后已发送邮件数: {len(sent_emails_filtered)}")
    
    for i, email in enumerate(sent_emails_filtered):
        is_recalled = email.get("is_recalled", False)
        status = "🔙已撤回" if is_recalled else ("✅已读" if email.get("is_read") else "📬未读")
        subject = email.get("subject", "(无主题)")[:30]
        to_addrs = str(email.get("to_addrs", []))[:20]
        
        print(f"  {i+1:2d}. {status:<8} {to_addrs:<20} {subject}")
    
    # 5. 检查可撤回邮件
    print("\n5️⃣ 检查可撤回邮件...")
    recallable_emails = email_service.get_recallable_emails(user_email)
    
    print(f"🔙 可撤回邮件数量: {len(recallable_emails)}")
    
    for i, email in enumerate(recallable_emails):
        subject = email.get("subject", "(无主题)")[:30]
        to_addrs = str(email.get("to_addrs", []))[:20]
        date = email.get("date", "")[:19]
        
        print(f"  {i+1:2d}. {date} {to_addrs:<20} {subject}")
    
    # 6. 总结
    print("\n" + "=" * 60)
    print("📊 状态总结")
    print("=" * 60)
    print(f"📥 收件箱邮件: {len(received_emails_all)} 封（其中 {recalled_count} 封已撤回）")
    print(f"📤 已发送邮件: {len(sent_emails_all)} 封（其中 {sent_recalled_count} 封已撤回）")
    print(f"🔙 可撤回邮件: {len(recallable_emails)} 封")
    
    # 检查过滤是否生效
    filter_working = (
        len(received_emails_filtered) == len(received_emails_all) - recalled_count and
        len(sent_emails_filtered) == len(sent_emails_all) - sent_recalled_count
    )
    
    print(f"\n🔧 撤回过滤功能: {'✅ 正常工作' if filter_working else '❌ 存在问题'}")
    
    if not filter_working:
        print("⚠️ 过滤功能可能存在问题，请检查:")
        print(f"   - 收件箱过滤: 期望 {len(received_emails_all) - recalled_count}，实际 {len(received_emails_filtered)}")
        print(f"   - 已发送过滤: 期望 {len(sent_emails_all) - sent_recalled_count}，实际 {len(sent_emails_filtered)}")
    
    return filter_working

def main():
    """主函数"""
    try:
        success = check_recall_status()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"检查过程中出现异常: {e}")
        print(f"❌ 检查过程中出现异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
