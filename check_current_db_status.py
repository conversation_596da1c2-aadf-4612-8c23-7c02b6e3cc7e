#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前数据库状态，审查撤回逻辑
"""

import sqlite3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

def check_database_status():
    """检查数据库中的邮件状态"""
    print("🔍 检查当前数据库状态")
    print("=" * 60)
    
    try:
        db_path = "data/email_db.sqlite"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        user_email = "<EMAIL>"
        
        # 1. 检查收件箱中的所有邮件
        print("1️⃣ 收件箱中的所有邮件:")
        cursor.execute("""
            SELECT message_id, subject, is_recalled, recalled_at, recalled_by, date
            FROM emails 
            WHERE to_addrs LIKE ? OR from_addr = ?
            ORDER BY date DESC
            LIMIT 10
        """, (f'%{user_email}%', user_email))
        
        inbox_emails = cursor.fetchall()
        print(f"📥 收件箱邮件总数: {len(inbox_emails)}")
        
        recalled_count_inbox = 0
        for i, email in enumerate(inbox_emails, 1):
            message_id, subject, is_recalled, recalled_at, recalled_by, date = email
            status = "🔙已撤回" if is_recalled == 1 else "📬正常"
            if is_recalled == 1:
                recalled_count_inbox += 1
            print(f"  {i}. {status} - {subject[:30]} ({message_id[:20]}...)")
            if is_recalled == 1:
                print(f"      撤回时间: {recalled_at}, 撤回者: {recalled_by}")
        
        print(f"📊 收件箱中已撤回邮件数: {recalled_count_inbox}")
        
        # 2. 检查已发送邮件中的所有邮件
        print("\n2️⃣ 已发送邮件中的所有邮件:")
        cursor.execute("""
            SELECT message_id, subject, is_recalled, recalled_at, recalled_by, date
            FROM sent_emails 
            WHERE from_addr = ?
            ORDER BY date DESC
            LIMIT 10
        """, (user_email,))
        
        sent_emails = cursor.fetchall()
        print(f"📤 已发送邮件总数: {len(sent_emails)}")
        
        recalled_count_sent = 0
        for i, email in enumerate(sent_emails, 1):
            message_id, subject, is_recalled, recalled_at, recalled_by, date = email
            status = "🔙已撤回" if is_recalled == 1 else "📬正常"
            if is_recalled == 1:
                recalled_count_sent += 1
            print(f"  {i}. {status} - {subject[:30]} ({message_id[:20]}...)")
            if is_recalled == 1:
                print(f"      撤回时间: {recalled_at}, 撤回者: {recalled_by}")
        
        print(f"📊 已发送中已撤回邮件数: {recalled_count_sent}")
        
        # 3. 查找特定的测试邮件
        print("\n3️⃣ 查找撤回测试邮件:")
        cursor.execute("""
            SELECT message_id, subject, is_recalled, recalled_at, recalled_by, date, 'inbox' as source
            FROM emails 
            WHERE (to_addrs LIKE ? OR from_addr = ?) AND subject LIKE '%撤回测试%'
            UNION ALL
            SELECT message_id, subject, is_recalled, recalled_at, recalled_by, date, 'sent' as source
            FROM sent_emails 
            WHERE from_addr = ? AND subject LIKE '%撤回测试%'
            ORDER BY date DESC
        """, (f'%{user_email}%', user_email, user_email))
        
        test_emails = cursor.fetchall()
        print(f"📧 找到撤回测试邮件: {len(test_emails)}封")
        
        for i, email in enumerate(test_emails, 1):
            message_id, subject, is_recalled, recalled_at, recalled_by, date, source = email
            status = "🔙已撤回" if is_recalled == 1 else "📬正常"
            source_icon = "📥" if source == "inbox" else "📤"
            print(f"  {i}. {source_icon} {status} - {subject} ({date})")
            print(f"      Message-ID: {message_id}")
            if is_recalled == 1:
                print(f"      撤回时间: {recalled_at}, 撤回者: {recalled_by}")
            else:
                print(f"      状态: 未撤回")
        
        # 4. 测试API查询结果
        print("\n4️⃣ 测试API查询结果:")
        
        from server.new_db_handler import EmailService
        service = EmailService()
        
        # 测试收件箱查询（不包含已撤回）
        api_inbox_normal = service.list_emails(
            user_email=user_email,
            include_recalled=False,
            limit=10
        )
        print(f"📥 API收件箱查询（不包含已撤回）: {len(api_inbox_normal)}封")
        
        # 测试收件箱查询（包含已撤回）
        api_inbox_all = service.list_emails(
            user_email=user_email,
            include_recalled=True,
            limit=10
        )
        print(f"📥 API收件箱查询（包含已撤回）: {len(api_inbox_all)}封")
        
        # 测试已发送查询（不包含已撤回）
        api_sent_normal = service.list_sent_emails(
            from_addr=user_email,
            include_recalled=False,
            limit=10
        )
        print(f"📤 API已发送查询（不包含已撤回）: {len(api_sent_normal)}封")
        
        # 测试已发送查询（包含已撤回）
        api_sent_all = service.list_sent_emails(
            from_addr=user_email,
            include_recalled=True,
            limit=10
        )
        print(f"📤 API已发送查询（包含已撤回）: {len(api_sent_all)}封")
        
        # 5. 分析差异
        print("\n5️⃣ 分析差异:")
        inbox_diff = len(api_inbox_all) - len(api_inbox_normal)
        sent_diff = len(api_sent_all) - len(api_sent_normal)
        
        print(f"📊 收件箱API过滤出的已撤回邮件: {inbox_diff}封")
        print(f"📊 已发送API过滤出的已撤回邮件: {sent_diff}封")
        print(f"📊 数据库中收件箱已撤回邮件: {recalled_count_inbox}封")
        print(f"📊 数据库中已发送已撤回邮件: {recalled_count_sent}封")
        
        # 6. 问题诊断
        print("\n6️⃣ 问题诊断:")
        
        if recalled_count_inbox == 0 and recalled_count_sent == 0:
            print("❌ 数据库中没有已撤回邮件")
            print("💡 可能原因:")
            print("   1. 撤回操作没有成功执行")
            print("   2. 撤回的邮件被删除了")
            print("   3. 撤回状态没有正确保存到数据库")
        elif inbox_diff == 0 and sent_diff == 0:
            print("❌ API过滤没有生效")
            print("💡 可能原因:")
            print("   1. include_recalled参数没有正确传递")
            print("   2. 数据库查询逻辑有问题")
        elif inbox_diff != recalled_count_inbox or sent_diff != recalled_count_sent:
            print("⚠️ API过滤结果与数据库不一致")
            print("💡 可能原因:")
            print("   1. 查询条件不匹配")
            print("   2. 数据同步问题")
        else:
            print("✅ 撤回功能正常工作")
            print("💡 如果CLI仍显示已撤回邮件，可能是:")
            print("   1. CLI使用了错误的查询参数")
            print("   2. CLI缓存了旧数据")
            print("   3. CLI代码没有更新")
        
        # 7. 检查最新的撤回测试邮件
        print("\n7️⃣ 检查最新的撤回测试邮件:")
        if test_emails:
            latest_test = test_emails[0]  # 最新的一封
            message_id, subject, is_recalled, recalled_at, recalled_by, date, source = latest_test
            
            print(f"📧 最新测试邮件: {subject}")
            print(f"📅 发送时间: {date}")
            print(f"🔙 撤回状态: {'已撤回' if is_recalled == 1 else '未撤回'}")
            
            if is_recalled == 1:
                print(f"📅 撤回时间: {recalled_at}")
                print(f"👤 撤回者: {recalled_by}")
                
                # 检查这封邮件在API查询中是否被正确过滤
                found_in_normal = any(email["message_id"] == message_id for email in api_inbox_normal)
                found_in_all = any(email["message_id"] == message_id for email in api_inbox_all)
                
                print(f"📊 在正常查询中找到: {'❌ 是（错误）' if found_in_normal else '✅ 否（正确）'}")
                print(f"📊 在完整查询中找到: {'✅ 是' if found_in_all else '❌ 否（错误）'}")
                
                if found_in_normal:
                    print("🚨 问题确认: 已撤回邮件仍在正常查询结果中！")
                else:
                    print("✅ 过滤正常: 已撤回邮件已被正确隐藏")
            else:
                print("ℹ️ 该邮件尚未撤回")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库状态时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    check_database_status()

if __name__ == "__main__":
    main()
