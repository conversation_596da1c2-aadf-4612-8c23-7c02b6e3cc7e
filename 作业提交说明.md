# CS3611 计算机网络课程设计 大作业提交说明

## 项目概述

本项目实现了一个功能完整的邮件系统，包含 SMTP/POP3 服务器、客户端以及 PGP 加密等扩展功能。系统支持 SSL/TLS 加密通信、用户认证、高并发处理和完整的邮件存储功能。

---

## 📂 实验提交内容

### 1. 代码实现

#### 1.1 客户端完整源码（支持发送/接收/附件功能）
**目录：`./client/`**

- **核心功能模块**
  - `smtp_client.py` - SMTP 客户端核心实现（22KB，553行）
  - `pop3_client_refactored.py` - POP3 客户端重构版本（9.8KB，315行）
  - `pop3_client_legacy.py` - POP3 客户端完整版本（53KB，1371行）
  - `mime_handler.py` - MIME 格式处理模块（8.1KB，275行）

- **PGP 加密支持**
  - `smtp_client_pgp.py` - 支持 PGP 加密的 SMTP 客户端（11KB，323行）
  - `pop3_client_pgp.py` - 支持 PGP 解密的 POP3 客户端（13KB，364行）

- **命令行接口**
  - `smtp_cli.py` - SMTP 命令行工具（13KB，377行）
  - `pop3_cli.py` - POP3 命令行工具（15KB，412行）
  - `smtp_cli_pgp.py` - 支持 PGP 的 SMTP 命令行工具（15KB，439行）

- **连接管理与安全**
  - `connection_pool.py` - 连接池管理（15KB，483行）
  - `pop3_connection_manager.py` - POP3 连接管理器（14KB，376行）
  - `security.py` - 安全功能模块（9.4KB，310行）
  - `socket_utils.py` - Socket 工具函数（2.2KB，87行）

#### 1.2 服务端脚本（含并发处理与数据库操作）
**目录：`./server/`**

- **核心服务器**
  - `smtp_server.py` - SMTP 服务器实现（21KB，551行）
  - `pop3_server.py` - POP3 服务器实现（32KB，848行）

- **数据库管理**
  - `new_db_handler.py` - 新版数据库处理器（39KB，1060行）
  - `db_connection_pool.py` - 数据库连接池（9.6KB，323行）
  - `email_repository.py` - 邮件存储仓库（22KB，658行）
  - `db_models.py` - 数据库模型定义（11KB，316行）
  - `migration_helper.py` - 数据库迁移工具（11KB，338行）

- **认证与会话管理**
  - `user_auth.py` - 用户认证模块（11KB，414行）
  - `pop3_session.py` - POP3 会话管理（9.7KB，266行）
  - `pop3_commands.py` - POP3 命令处理（31KB，850行）

- **内容管理**
  - `email_content_manager.py` - 邮件内容管理器（20KB，528行）

#### 1.3 扩展功能代码
**主要分布在以下目录：**

- **PGP 端到端加密**（`./pgp/`）
  - `pgp_manager.py` - PGP 密钥和加密管理（23KB，603行）
  - `key_manager.py` - 密钥管理器（16KB，460行）
  - `email_crypto.py` - 邮件加密/解密（20KB，454行）
  - `pgp_cli.py` - PGP 命令行界面（19KB，532行）

- **Web 界面**（根目录）
  - `simple_web_client.py` - Web 邮件客户端（31KB，853行）
  - `run_simple_web.py` - Web 服务启动脚本（1.2KB，49行）
  - `templates/` - HTML 模板文件
    - `simple_index.html` - 主页界面
    - `simple_send.html` - 发送邮件界面
    - `simple_receive.html` - 接收邮件界面
    - `simple_add_account.html` - 账户配置界面

- **其他功能模块**
  - `pgp_cli.py` - 根目录 PGP 命令行工具（29KB，799行）
  - `email_providers_config.py` - 邮件提供商配置（6.0KB，211行）
  - `user_manager.py` - 用户管理模块（9.5KB，308行）
  - `spam_filter/` - 垃圾邮件过滤模块

---

### 2. 设计文档

#### 2.1 协议交互流程图
**目录：`./charts/`**

- **邮件协议流程图**
  - `email_protocol_flow-1.pdf` - 详细协议交互流程（211KB）
  - `email_protocol_flow-2.pdf` - SMTP/POP3 交互时序图（101KB）
  - `email_protocol_flow-3.pdf` - 简化协议流程（48KB）
  - `email_protocol_flow-4.pdf` - 认证流程图（60KB）

#### 2.2 MIME 结构示例
**目录：`./charts/`**

- **MIME 结构图表**
  - `mime_structure-1.pdf` - 基础 MIME 结构（37KB）
  - `mime_structure-2.pdf` - 多部分 MIME 详解（95KB）
  - `mime_structure-3.pdf` - 附件处理结构（51KB）
  - `MIME_Structure_Diagram.pdf` - 综合 MIME 结构图（117KB）

#### 2.3 SSL 握手过程
**目录：`./charts/`**

- **TLS 握手流程图**
  - `tls_handshake-1.pdf` - 完整 TLS 握手过程（118KB）
  - `tls_handshake-2.pdf` - 证书验证流程（116KB）
  - `tls_handshake-3.pdf` - 密钥交换过程（103KB）
  - `tls_handshake-4.pdf` - 加密参数协商（64KB）
  - `tls_handshake-5.pdf` - 连接建立确认（46KB）
  - `tls_handshake-6.pdf` - 错误处理流程（59KB）
  - `tls_handshake-7.pdf` - 优化握手过程（59KB）

#### 2.4 技术文档（根目录）
- `邮件协议交互流程图.md` - 邮件协议详细分析（10KB，312行）
- `邮件协议交互流程详细分析.md` - 深入协议分析（11KB，367行）
- `MIME_RFC_COMPREHENSIVE_RESEARCH.md` - MIME RFC 规范研究（10KB，281行）
- `MIME_Structure_Visual_Diagram.md` - MIME 结构可视化说明（16KB，342行）
- `TLS_PROTOCOL_COMPREHENSIVE_RESEARCH.md` - TLS 协议全面研究（22KB，799行）
- `TLS_HANDSHAKE_FLOWCHARTS.md` - TLS 握手流程图说明（14KB，427行）
- `TLS_ALERT_MESSAGES_ANALYSIS.md` - TLS 警报消息分析（18KB，657行）
- `PROJECT_ARCHITECTURE.md` - 项目架构设计文档（23KB，866行）

#### 2.5 学术海报
**目录：`./charts/`**
- `academic_poster_cs3611.pdf` - 项目学术海报（286KB）

---

### 3. 测试报告

#### 3.1 并发压力测试结果
**根目录下的测试报告文件：**

- **50 用户并发测试**
  - `50并发压力测试报告.html` - 50 并发用户测试详细报告（17KB，565行）
  - **测试结果摘要**：
    - 成功发送：50 封邮件
    - 成功接收：50 封邮件
    - 正确匹配：50 封邮件
    - 匹配率：100.0%

- **200 用户并发测试**
  - `200并发压力测试报告.html` - 200 并发用户压力测试报告（17KB，538行）
  - **测试结果表明系统能够稳定处理高并发负载**

#### 3.2 测试框架与工具
**目录：`./tests/`**
- 单元测试、集成测试和性能测试模块
- 测试输出目录：`./test_output/`

---

### 4. 用户手册

#### 4.1 客户端配置说明

**注意：** 本节将配合用户截图演示一起完成。以下是初步的配置指南框架：

##### 4.1.1 系统要求
- **操作系统**：Windows 10/11, macOS, Linux
- **Python 版本**：3.8 或更高版本
- **网络要求**：支持 SMTP/POP3 协议的网络环境

##### 4.1.2 快速开始

**最简单的使用方式：**
```bash
# 安装依赖
pip install -r requirements.txt

# 初始化环境
python init_project.py

# 启动统一CLI界面
python cli.py
```

##### 4.1.3 服务器配置参数

**SMTP 服务器配置：**
- **默认端口**：8025（非加密），465（SSL加密）
- **认证方式**：用户名/密码认证
- **加密支持**：SSL/TLS

**POP3 服务器配置：**
- **默认端口**：8110（非加密），995（SSL加密）
- **认证方式**：用户名/密码认证
- **加密支持**：SSL/TLS

##### 4.1.4 SSL 证书配置

**自动 SSL 推断：**
- 系统在使用标准 SSL 端口（465, 587, 993, 995）时自动启用 SSL
- 可通过 `--ssl` 参数显式控制 SSL 启用/禁用

**证书文件位置：**
- 证书目录：`./certs/`（如果存在证书文件）
- 系统支持自签名证书用于测试环境

##### 4.1.5 参数优先级系统

**配置参数按以下优先级处理：**
1. **命令行参数**（最高优先级）
2. 配置文件设置
3. 环境变量
4. 系统默认值

**示例命令：**
```bash
# 发送邮件（指定服务器和端口）
python -m client.smtp_cli \
  --host localhost --port 8025 \
  --username testuser --password testpass \
  --from <EMAIL> --to <EMAIL> \
  --subject "测试邮件" --body "邮件内容"

# 接收邮件（SSL端口自动启用SSL）
python -m client.pop3_cli \
  --host localhost --port 995 \
  --username testuser --password testpass --list
```

##### 4.1.6 PGP 加密配置

**PGP 功能使用：**
```bash
# 启动 PGP 加密邮件客户端
python pgp_cli.py

# 运行 PGP 完整演示
python demo_pgp_with_auth.py
```

**PGP 测试用户：**
- 用户名：`pgptest`
- 密码：`pgp123`

##### 4.1.7 Web 界面访问

**启动 Web 客户端：**
```bash
python run_simple_web.py
```

**Web 界面功能：**
- 邮件发送和接收
- 账户管理
- 附件处理
- 响应式设计界面

**（详细的界面配置说明将配合截图演示补充完整）**

---

### 5. 项目特色与创新点

#### 5.1 技术创新
- **智能 SSL 推断**：根据端口自动启用/禁用 SSL 加密
- **参数优先级系统**：完善的配置管理机制
- **高并发优化**：支持 200+ 并发连接，性能稳定
- **PGP 端到端加密**：完整的加密邮件解决方案

#### 5.2 系统特性
- **多客户端支持**：命令行、Web界面、PGP客户端
- **完整的协议实现**：SMTP/POP3 协议完全支持
- **数据库优化**：SQLite WAL 模式，连接池管理
- **安全性保障**：SSL/TLS 加密，用户认证，权限管理

#### 5.3 扩展功能
- **垃圾邮件过滤**：智能过滤机制
- **邮件格式支持**：HTML/纯文本/MIME 多部分邮件
- **附件处理**：完整的附件编码/解码功能
- **用户管理**：用户注册、认证、权限管理

---

### 6. 演示内容说明

#### 6.1 发送带附件的邮件并展示接收端完整解析
- **演示脚本**：`demo_attachment_email.py`（12KB，357行）
- **演示说明**：`ATTACHMENT_DEMO_README.md`（5.7KB，205行）
- **演示内容**：
  - 发送包含多种格式附件的邮件
  - 接收端完整解析 MIME 结构
  - 附件提取和保存演示
  - 邮件头部信息解析

#### 6.2 模拟中间人攻击，展示SSL加密防护效果
- **相关文档**：`docs/REALISTIC_MITM_GUIDE.md`（4.8KB，165行）
- **安全指南**：`docs/email_security_guide.md`（13KB，405行）
- **演示内容**：
  - 非加密连接的数据包捕获
  - SSL/TLS 加密连接的保护效果
  - 证书验证机制演示
  - 中间人攻击防护验证

---

### 7. 文件结构总览

```
cs3611_email/
├── client/                    # 客户端源码（16个核心文件）
├── server/                    # 服务端源码（16个核心文件）
├── pgp/                       # PGP加密功能（5个文件）
├── charts/                    # 设计文档图表（32个PDF/SVG文件）
├── docs/                      # 技术文档（30个MD文件）
├── templates/                 # Web界面模板（6个HTML文件）
├── tests/                     # 测试框架
├── 50并发压力测试报告.html      # 并发测试报告
├── 200并发压力测试报告.html     # 高并发测试报告
├── README.md                  # 项目说明
├── PROJECT_ARCHITECTURE.md    # 架构设计文档
├── 邮件协议交互流程图.md         # 协议分析文档
├── TLS_PROTOCOL_COMPREHENSIVE_RESEARCH.md  # TLS研究文档
├── MIME_RFC_COMPREHENSIVE_RESEARCH.md      # MIME研究文档
└── requirements.txt           # 依赖包列表
```

---

### 8. 运行环境与依赖

**Python 包依赖（requirements.txt）：**
- cryptography==41.0.7
- PyQt5==5.15.10
- Flask==3.0.0
- sqlite3（Python标准库）
- 其他网络和加密相关包

**系统要求：**
- Python 3.8+
- Windows/macOS/Linux
- 2GB+ 可用内存
- 网络连接（用于SSL证书验证）

---

### 9. 提交说明

本项目包含了完整的邮件系统实现，从底层协议到用户界面都有详细的实现和文档。所有代码都经过测试验证，文档齐全，符合课程设计要求。

**主要成果：**
- ✅ 完整的 SMTP/POP3 服务器实现
- ✅ 多样化的客户端（CLI/Web/PGP）
- ✅ 详细的技术文档和设计图表
- ✅ 全面的并发性能测试
- ✅ PGP 端到端加密扩展
- ✅ SSL/TLS 安全通信保护

**演示视频将展示：**
1. 系统完整功能演示
2. 附件邮件发送和接收
3. SSL 加密防护效果
4. PGP 端到端加密演示
5. 高并发性能验证

---

**提交时间：** 2025年6月

**项目规模：** 约 10,000+ 行代码，50+ 文件

**测试覆盖：** 单元测试、集成测试、性能测试、安全测试
