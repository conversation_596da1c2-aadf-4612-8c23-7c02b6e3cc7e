#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CLI邮件撤回功能测试脚本

模拟用户在CLI中查看邮件列表，验证撤回邮件是否被正确隐藏
"""

import os
import sys
import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent))

from common.utils import setup_logging
from server.new_db_handler import EmailService
from common.models import Email, EmailAddress
from server.db_models import SentEmailRecord

# 设置日志
logger = setup_logging("test_cli_recall")

def test_cli_email_recall():
    """测试CLI中的邮件撤回功能"""
    print("=" * 60)
    print("📋 CLI邮件撤回功能测试")
    print("=" * 60)
    
    # 初始化邮件服务
    email_service = EmailService()
    
    # 测试用户邮箱
    test_user = "<EMAIL>"
    test_recipient = "<EMAIL>"
    
    print(f"📧 测试发件人: {test_user}")
    print(f"📨 测试收件人: {test_recipient}")
    
    # 1. 创建测试邮件
    print("\n1️⃣ 创建测试邮件...")
    test_email = Email(
        message_id=f"cli-test-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}@test.local",
        subject="CLI撤回测试邮件",
        from_addr=EmailAddress("测试用户1", test_user),
        to_addrs=[EmailAddress("测试用户2", test_recipient)],
        cc_addrs=[],
        bcc_addrs=[],
        date=datetime.datetime.now(),
        text_content="这是一封用于测试CLI撤回功能的邮件。",
        html_content="",
        attachments=[]
    )
    
    # 2. 保存邮件
    print("2️⃣ 保存邮件...")
    try:
        # 保存到收件箱
        email_service.save_email_metadata(
            message_id=test_email.message_id,
            from_addr=test_email.from_addr.address,
            to_addrs=[addr.address for addr in test_email.to_addrs],
            subject=test_email.subject,
            date=test_email.date,
            size=len(test_email.text_content),
        )
        
        # 保存到已发送邮件
        sent_record = SentEmailRecord(
            message_id=test_email.message_id,
            from_addr=test_email.from_addr.address,
            to_addrs=[addr.address for addr in test_email.to_addrs],
            cc_addrs=[],
            bcc_addrs=[],
            subject=test_email.subject,
            date=test_email.date,
            size=len(test_email.text_content),
        )
        email_service.email_repo.create_sent_email(sent_record)
        print(f"✅ 邮件已保存: {test_email.message_id}")
        
    except Exception as e:
        print(f"❌ 保存邮件时出错: {e}")
        return False
    
    # 3. 模拟CLI查看收件箱（撤回前）
    print("\n3️⃣ 模拟CLI查看收件箱（撤回前）...")
    received_emails = email_service.list_emails(
        user_email=test_recipient,
        include_spam=False,
        is_spam=False,
        include_recalled=False,  # CLI默认不显示已撤回邮件
    )
    
    found_in_inbox = any(email["message_id"] == test_email.message_id for email in received_emails)
    print(f"📥 收件箱邮件数量: {len(received_emails)}")
    print(f"📧 找到测试邮件: {'✅ 是' if found_in_inbox else '❌ 否'}")
    
    # 4. 模拟CLI查看已发送邮件（撤回前）
    print("\n4️⃣ 模拟CLI查看已发送邮件（撤回前）...")
    sent_emails = email_service.list_sent_emails(
        from_addr=test_user,
        include_spam=False,
        is_spam=False,
        include_recalled=False,  # CLI默认不显示已撤回邮件
    )
    
    found_in_sent = any(email["message_id"] == test_email.message_id for email in sent_emails)
    print(f"📤 已发送邮件数量: {len(sent_emails)}")
    print(f"📧 找到测试邮件: {'✅ 是' if found_in_sent else '❌ 否'}")
    
    # 5. 撤回邮件
    print("\n5️⃣ 撤回邮件...")
    recall_result = email_service.recall_email(test_email.message_id, test_user)
    if recall_result["success"]:
        print(f"✅ {recall_result['message']}")
    else:
        print(f"❌ {recall_result['message']}")
        return False
    
    # 6. 模拟CLI查看收件箱（撤回后）
    print("\n6️⃣ 模拟CLI查看收件箱（撤回后）...")
    received_emails_after = email_service.list_emails(
        user_email=test_recipient,
        include_spam=False,
        is_spam=False,
        include_recalled=False,  # CLI默认不显示已撤回邮件
    )
    
    found_in_inbox_after = any(email["message_id"] == test_email.message_id for email in received_emails_after)
    print(f"📥 收件箱邮件数量: {len(received_emails_after)}")
    print(f"📧 找到测试邮件: {'❌ 是（错误）' if found_in_inbox_after else '✅ 否（正确）'}")
    
    # 7. 模拟CLI查看已发送邮件（撤回后）
    print("\n7️⃣ 模拟CLI查看已发送邮件（撤回后）...")
    sent_emails_after = email_service.list_sent_emails(
        from_addr=test_user,
        include_spam=False,
        is_spam=False,
        include_recalled=False,  # CLI默认不显示已撤回邮件
    )
    
    found_in_sent_after = any(email["message_id"] == test_email.message_id for email in sent_emails_after)
    print(f"📤 已发送邮件数量: {len(sent_emails_after)}")
    print(f"📧 找到测试邮件: {'❌ 是（错误）' if found_in_sent_after else '✅ 否（正确）'}")
    
    # 8. 显示邮件列表详情（用于调试）
    print("\n8️⃣ 邮件列表详情...")
    print("收件箱邮件:")
    for i, email in enumerate(received_emails_after[:3], 1):  # 只显示前3封
        status = "🔙已撤回" if email.get("is_recalled") else ("✅已读" if email.get("is_read") else "📬未读")
        print(f"  {i}. {status} {email.get('subject', '(无主题)')[:30]}")
    
    print("已发送邮件:")
    for i, email in enumerate(sent_emails_after[:3], 1):  # 只显示前3封
        status = "🔙已撤回" if email.get("is_recalled") else ("✅已读" if email.get("is_read") else "📬未读")
        print(f"  {i}. {status} {email.get('subject', '(无主题)')[:30]}")
    
    # 9. 清理测试数据
    print("\n9️⃣ 清理测试数据...")
    try:
        email_service.delete_email_metadata(test_email.message_id)
        email_service.delete_sent_email_metadata(test_email.message_id)
        print("✅ 测试数据已清理")
    except Exception as e:
        print(f"⚠️ 清理测试数据时出错: {e}")
    
    # 10. 测试结果
    print("\n" + "=" * 60)
    print("📊 CLI测试结果")
    print("=" * 60)
    
    success = (
        found_in_inbox and found_in_sent and  # 撤回前可见
        not found_in_inbox_after and not found_in_sent_after  # 撤回后不可见
    )
    
    if success:
        print("🎉 CLI邮件撤回功能测试通过！")
        print("✅ 撤回前：收件人和发件人都能看到邮件")
        print("✅ 撤回后：收件人和发件人都看不到邮件")
        print("✅ CLI邮件列表正确隐藏已撤回邮件")
    else:
        print("❌ CLI邮件撤回功能测试失败！")
        if found_in_inbox_after:
            print("❌ 撤回后收件人仍能看到邮件")
        if found_in_sent_after:
            print("❌ 撤回后发件人仍能看到邮件")
    
    return success

def main():
    """主函数"""
    try:
        success = test_cli_email_recall()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        print(f"❌ 测试过程中出现异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
