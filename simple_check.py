#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from server.new_db_handler import EmailService

def main():
    print("🔍 简单检查邮件撤回状态")
    
    service = EmailService()
    user_email = "<EMAIL>"
    
    # 检查收件箱邮件（包含已撤回）
    all_emails = service.list_emails(
        user_email=user_email,
        include_recalled=True,
        limit=10
    )
    
    # 检查收件箱邮件（不包含已撤回）
    normal_emails = service.list_emails(
        user_email=user_email,
        include_recalled=False,
        limit=10
    )
    
    print(f"📊 总邮件数（包含已撤回）: {len(all_emails)}")
    print(f"📊 正常邮件数（不包含已撤回）: {len(normal_emails)}")
    print(f"📊 已撤回邮件数: {len(all_emails) - len(normal_emails)}")
    
    print("\n📋 所有邮件列表（包含已撤回）:")
    for i, email in enumerate(all_emails, 1):
        is_recalled = email.get("is_recalled", False)
        status = "🔙已撤回" if is_recalled else "📬正常"
        subject = email.get("subject", "(无主题)")[:30]
        print(f"  {i}. {status} - {subject}")
    
    print("\n📋 正常邮件列表（不包含已撤回）:")
    for i, email in enumerate(normal_emails, 1):
        is_recalled = email.get("is_recalled", False)
        status = "🔙已撤回" if is_recalled else "📬正常"
        subject = email.get("subject", "(无主题)")[:30]
        print(f"  {i}. {status} - {subject}")
    
    # 检查已发送邮件
    sent_all = service.list_sent_emails(
        from_addr=user_email,
        include_recalled=True,
        limit=10
    )
    
    sent_normal = service.list_sent_emails(
        from_addr=user_email,
        include_recalled=False,
        limit=10
    )
    
    print(f"\n📤 已发送邮件总数（包含已撤回）: {len(sent_all)}")
    print(f"📤 已发送正常邮件数（不包含已撤回）: {len(sent_normal)}")
    print(f"📤 已撤回的已发送邮件数: {len(sent_all) - len(sent_normal)}")

if __name__ == "__main__":
    main()
