#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试撤回功能修复
"""

import sys
import datetime
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from server.new_db_handler import EmailService
from server.db_models import SentEmailRecord

def test_recall_fix():
    """测试撤回功能修复"""
    print("🔧 测试撤回功能修复")
    print("=" * 50)
    
    service = EmailService()
    user_email = "<EMAIL>"
    recipient_email = "<EMAIL>"
    
    # 1. 创建测试邮件
    print("1️⃣ 创建测试邮件...")
    test_message_id = f"test-recall-fix-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}@test.local"
    
    # 保存到收件箱
    service.save_email_metadata(
        message_id=test_message_id,
        from_addr=user_email,
        to_addrs=[recipient_email],
        subject="撤回功能修复测试",
        date=datetime.datetime.now(),
        size=100,
    )
    
    # 保存到已发送邮件
    sent_record = SentEmailRecord(
        message_id=test_message_id,
        from_addr=user_email,
        to_addrs=[recipient_email],
        cc_addrs=[],
        bcc_addrs=[],
        subject="撤回功能修复测试",
        date=datetime.datetime.now(),
        size=100,
    )
    service.email_repo.create_sent_email(sent_record)
    print(f"✅ 测试邮件已创建: {test_message_id}")
    
    # 2. 验证邮件存在
    print("\n2️⃣ 验证邮件存在...")
    received_emails = service.list_emails(user_email=recipient_email, include_recalled=True, limit=10)
    sent_emails = service.list_sent_emails(from_addr=user_email, include_recalled=True, limit=10)
    
    found_received = any(email["message_id"] == test_message_id for email in received_emails)
    found_sent = any(email["message_id"] == test_message_id for email in sent_emails)
    
    print(f"📥 收件箱中找到: {'✅ 是' if found_received else '❌ 否'}")
    print(f"📤 已发送中找到: {'✅ 是' if found_sent else '❌ 否'}")
    
    if not found_received or not found_sent:
        print("❌ 测试邮件创建失败")
        return False
    
    # 3. 测试撤回功能
    print("\n3️⃣ 测试撤回功能...")
    recall_result = service.recall_email(test_message_id, user_email)
    
    if recall_result["success"]:
        print(f"✅ 撤回成功: {recall_result['message']}")
    else:
        print(f"❌ 撤回失败: {recall_result['message']}")
        return False
    
    # 4. 验证撤回状态
    print("\n4️⃣ 验证撤回状态...")
    
    # 检查数据库中的撤回状态
    import sqlite3
    db_path = "data/email_db.sqlite"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查收件箱中的撤回状态
    cursor.execute("""
        SELECT is_recalled, recalled_at, recalled_by 
        FROM emails 
        WHERE message_id = ?
    """, (test_message_id,))
    
    email_result = cursor.fetchone()
    if email_result:
        is_recalled, recalled_at, recalled_by = email_result
        print(f"📥 收件箱撤回状态: is_recalled={is_recalled}, recalled_at={recalled_at}, recalled_by={recalled_by}")
    else:
        print("❌ 收件箱中未找到邮件")
    
    # 检查已发送邮件中的撤回状态
    cursor.execute("""
        SELECT is_recalled, recalled_at, recalled_by 
        FROM sent_emails 
        WHERE message_id = ?
    """, (test_message_id,))
    
    sent_result = cursor.fetchone()
    if sent_result:
        is_recalled, recalled_at, recalled_by = sent_result
        print(f"📤 已发送撤回状态: is_recalled={is_recalled}, recalled_at={recalled_at}, recalled_by={recalled_by}")
    else:
        print("❌ 已发送中未找到邮件")
    
    conn.close()
    
    # 5. 测试过滤效果
    print("\n5️⃣ 测试过滤效果...")
    
    # 不包含已撤回邮件
    received_normal = service.list_emails(user_email=recipient_email, include_recalled=False, limit=10)
    sent_normal = service.list_sent_emails(from_addr=user_email, include_recalled=False, limit=10)
    
    found_received_normal = any(email["message_id"] == test_message_id for email in received_normal)
    found_sent_normal = any(email["message_id"] == test_message_id for email in sent_normal)
    
    print(f"📥 收件箱中找到（不包含已撤回）: {'❌ 是（错误）' if found_received_normal else '✅ 否（正确）'}")
    print(f"📤 已发送中找到（不包含已撤回）: {'❌ 是（错误）' if found_sent_normal else '✅ 否（正确）'}")
    
    # 包含已撤回邮件
    received_all = service.list_emails(user_email=recipient_email, include_recalled=True, limit=10)
    sent_all = service.list_sent_emails(from_addr=user_email, include_recalled=True, limit=10)
    
    found_received_all = any(email["message_id"] == test_message_id for email in received_all)
    found_sent_all = any(email["message_id"] == test_message_id for email in sent_all)
    
    print(f"📥 收件箱中找到（包含已撤回）: {'✅ 是' if found_received_all else '❌ 否'}")
    print(f"📤 已发送中找到（包含已撤回）: {'✅ 是' if found_sent_all else '❌ 否'}")
    
    # 6. 清理测试数据
    print("\n6️⃣ 清理测试数据...")
    try:
        service.delete_email_metadata(test_message_id)
        service.delete_sent_email_metadata(test_message_id)
        print("✅ 测试数据已清理")
    except Exception as e:
        print(f"⚠️ 清理测试数据时出错: {e}")
    
    # 7. 测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    
    success = (
        email_result and email_result[0] == 1 and  # 收件箱中已撤回
        sent_result and sent_result[0] == 1 and    # 已发送中已撤回
        not found_received_normal and              # 过滤生效（收件箱）
        not found_sent_normal and                  # 过滤生效（已发送）
        found_received_all and                     # 包含已撤回时可见（收件箱）
        found_sent_all                             # 包含已撤回时可见（已发送）
    )
    
    if success:
        print("🎉 撤回功能修复成功！")
        print("✅ 撤回操作正常工作")
        print("✅ 数据库状态正确更新")
        print("✅ 邮件过滤正确生效")
    else:
        print("❌ 撤回功能修复失败！")
        if not email_result or email_result[0] != 1:
            print("❌ 收件箱撤回状态未正确更新")
        if not sent_result or sent_result[0] != 1:
            print("❌ 已发送撤回状态未正确更新")
        if found_received_normal or found_sent_normal:
            print("❌ 邮件过滤未生效")
    
    return success

def main():
    """主函数"""
    try:
        success = test_recall_fix()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
