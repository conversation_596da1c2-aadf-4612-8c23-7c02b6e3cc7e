# 简化Web邮件客户端

## 概述

这是一个基于CLI底层实现的简化Web邮件客户端，直接复用了CLI模块中已经验证过的稳定邮件发送逻辑，避免了复杂的web层封装导致的问题。

## 主要特点

- ✅ **基于CLI底层**: 直接使用CLI模块的SMTP和POP3客户端
- ✅ **稳定可靠**: 复用已验证的邮件发送逻辑
- ✅ **简洁界面**: 现代化Bootstrap界面设计
- ✅ **完整功能**: 支持登录、发送、接收、附件
- ✅ **无复杂封装**: 避免过度抽象导致的问题

## 快速开始

### 1. 启动应用

```bash
# 方式一：使用启动脚本
python run_simple_web.py

# 方式二：直接启动
python simple_web_client.py
```

### 2. 访问界面

打开浏览器访问：http://localhost:3000

### 3. 添加邮箱账户

首次使用需要添加邮箱账户：

1. 点击"添加邮箱账户"
2. 填写邮箱地址和显示名称
3. 配置SMTP设置（发送邮件）
4. 配置POP3设置（接收邮件）
5. 保存配置

## 支持的邮箱服务商

### QQ邮箱
- SMTP: smtp.qq.com:587 (SSL)
- POP3: pop.qq.com:995 (SSL)
- **注意**: 需要使用授权码，不是QQ密码

### Gmail
- SMTP: smtp.gmail.com:587 (TLS)
- POP3: pop.gmail.com:995 (SSL)
- **注意**: 需要开启两步验证并使用应用专用密码

### 163邮箱
- SMTP: smtp.163.com:994 (SSL)
- POP3: pop.163.com:995 (SSL)
- **注意**: 需要开启SMTP/POP3服务

### Outlook
- SMTP: smtp-mail.outlook.com:587 (TLS)
- POP3: outlook.office365.com:995 (SSL)

## 功能说明

### 登录
- 输入已配置的邮箱地址登录
- 系统会自动加载对应的邮箱配置

### 发送邮件
- 支持多个收件人（用逗号分隔）
- 支持抄送（CC）和密送（BCC）
- 支持附件上传
- 实时邮箱地址验证

### 接收邮件
- 显示最新20封邮件
- 支持查看邮件详情
- 支持回复功能
- 显示附件信息

### 账户管理
- 添加多个邮箱账户
- 自动识别常见邮箱服务商配置
- 状态检查功能

## 技术架构

```
简化Web客户端
├── simple_web_client.py     # 主应用文件
├── templates/               # HTML模板
│   ├── base.html           # 基础模板
│   ├── simple_login.html   # 登录页面
│   ├── simple_index.html   # 首页
│   ├── simple_add_account.html # 添加账户
│   ├── simple_send.html    # 发送邮件
│   └── simple_receive.html # 接收邮件
└── WebCLIBridge            # CLI桥接类
    ├── 直接使用AccountManager
    ├── 直接使用SMTPClient
    └── 直接使用POP3Client
```

## 与原Web版本的区别

| 特性     | 原Web版本            | 简化版本            |
| -------- | -------------------- | ------------------- |
| 架构     | 复杂的web层封装      | 直接使用CLI底层     |
| 初始化   | 多重初始化，容易出错 | 单一桥接，简洁稳定  |
| 邮件发送 | 自定义SMTP逻辑       | 复用CLI验证过的逻辑 |
| 依赖     | 多个web相关包        | 仅依赖Flask         |
| 维护性   | 复杂，难以调试       | 简单，易于维护      |

## 为什么选择这种方案

1. **复用成功的逻辑**: CLI版本的邮件发送功能已经过验证，稳定可靠
2. **避免重复造轮子**: 直接使用已有的SMTP和POP3客户端
3. **减少出错可能**: 简化架构，减少封装层级
4. **便于调试**: 问题更容易定位和解决
5. **最小改动原则**: 在已有基础上做最小的改动

## 故障排除

### 邮件发送失败
1. 检查SMTP配置是否正确
2. 确认邮箱服务商的授权码/密码
3. 检查网络连接
4. 查看控制台日志

### 邮件接收失败
1. 检查POP3配置是否正确
2. 确认邮箱服务已开启POP3
3. 检查用户名和密码

### 无法登录
1. 确认邮箱账户已添加
2. 检查账户配置文件是否存在
3. 尝试重新添加账户

## 开发说明

如果需要扩展功能，建议：

1. 在`WebCLIBridge`类中添加新方法
2. 直接调用CLI模块的现有功能
3. 避免重新实现邮件协议逻辑
4. 保持简洁的架构设计

## 总结

这个简化版本通过直接复用CLI的底层逻辑，提供了一个稳定可靠的Web邮件客户端。它避免了复杂web封装可能带来的问题，同时保持了完整的功能性和良好的用户体验。 