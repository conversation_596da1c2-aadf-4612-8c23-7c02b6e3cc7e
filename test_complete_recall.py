#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的邮件撤回功能测试
测试从发送到撤回的完整流程
"""

import sys
import datetime
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from server.new_db_handler import EmailService
from server.db_models import SentEmailRecord

def test_complete_recall_flow():
    """测试完整的邮件撤回流程"""
    print("🔄 完整邮件撤回流程测试")
    print("=" * 60)
    
    service = EmailService()
    sender_email = "<EMAIL>"
    recipient_email = "<EMAIL>"
    
    # 1. 模拟发送邮件（SMTP服务器接收邮件）
    print("1️⃣ 模拟发送邮件...")
    test_message_id = f"complete-test-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}@test.local"
    test_subject = "完整撤回流程测试邮件"
    
    # 保存到收件箱（模拟SMTP服务器接收邮件）
    service.save_email_metadata(
        message_id=test_message_id,
        from_addr=sender_email,
        to_addrs=[recipient_email],
        subject=test_subject,
        date=datetime.datetime.now(),
        size=200,
    )
    
    # 保存到已发送邮件
    sent_record = SentEmailRecord(
        message_id=test_message_id,
        from_addr=sender_email,
        to_addrs=[recipient_email],
        cc_addrs=[],
        bcc_addrs=[],
        subject=test_subject,
        date=datetime.datetime.now(),
        size=200,
    )
    service.email_repo.create_sent_email(sent_record)
    print(f"✅ 邮件已发送并保存: {test_message_id}")
    
    # 2. 验证收件人可以看到邮件（模拟POP3查询）
    print("\n2️⃣ 验证收件人可以看到邮件...")
    recipient_emails = service.list_emails(
        user_email=recipient_email,
        include_recalled=False,  # POP3服务器默认不显示已撤回邮件
        limit=10
    )
    
    found_before_recall = any(email["message_id"] == test_message_id for email in recipient_emails)
    print(f"📥 收件人收件箱中找到邮件: {'✅ 是' if found_before_recall else '❌ 否'}")
    print(f"📊 收件人收件箱邮件总数: {len(recipient_emails)}")
    
    # 3. 验证发件人可以看到已发送邮件
    print("\n3️⃣ 验证发件人可以看到已发送邮件...")
    sender_sent_emails = service.list_sent_emails(
        from_addr=sender_email,
        include_recalled=False,  # CLI默认不显示已撤回邮件
        limit=10
    )
    
    found_sent_before_recall = any(email["message_id"] == test_message_id for email in sender_sent_emails)
    print(f"📤 发件人已发送中找到邮件: {'✅ 是' if found_sent_before_recall else '❌ 否'}")
    print(f"📊 发件人已发送邮件总数: {len(sender_sent_emails)}")
    
    if not found_before_recall or not found_sent_before_recall:
        print("❌ 邮件发送验证失败")
        return False
    
    # 4. 执行撤回操作
    print("\n4️⃣ 执行撤回操作...")
    recall_result = service.recall_email(test_message_id, sender_email)
    
    if recall_result["success"]:
        print(f"✅ 撤回成功: {recall_result['message']}")
    else:
        print(f"❌ 撤回失败: {recall_result['message']}")
        return False
    
    # 5. 验证收件人看不到邮件（模拟POP3查询）
    print("\n5️⃣ 验证收件人看不到邮件...")
    recipient_emails_after = service.list_emails(
        user_email=recipient_email,
        include_recalled=False,  # POP3服务器不显示已撤回邮件
        limit=10
    )
    
    found_after_recall = any(email["message_id"] == test_message_id for email in recipient_emails_after)
    print(f"📥 收件人收件箱中找到邮件（撤回后）: {'❌ 是（错误）' if found_after_recall else '✅ 否（正确）'}")
    print(f"📊 收件人收件箱邮件总数（撤回后）: {len(recipient_emails_after)}")
    
    # 6. 验证发件人看不到已发送邮件（CLI查询）
    print("\n6️⃣ 验证发件人看不到已发送邮件...")
    sender_sent_emails_after = service.list_sent_emails(
        from_addr=sender_email,
        include_recalled=False,  # CLI不显示已撤回邮件
        limit=10
    )
    
    found_sent_after_recall = any(email["message_id"] == test_message_id for email in sender_sent_emails_after)
    print(f"📤 发件人已发送中找到邮件（撤回后）: {'❌ 是（错误）' if found_sent_after_recall else '✅ 否（正确）'}")
    print(f"📊 发件人已发送邮件总数（撤回后）: {len(sender_sent_emails_after)}")
    
    # 7. 验证使用include_recalled=True时可以看到已撤回邮件
    print("\n7️⃣ 验证管理员可以看到已撤回邮件...")
    
    # 收件人使用include_recalled=True
    recipient_all_emails = service.list_emails(
        user_email=recipient_email,
        include_recalled=True,  # 包含已撤回邮件
        limit=10
    )
    
    found_with_recalled = any(email["message_id"] == test_message_id for email in recipient_all_emails)
    print(f"📥 收件人收件箱（包含已撤回）: {'✅ 找到' if found_with_recalled else '❌ 未找到'}")
    
    # 发件人使用include_recalled=True
    sender_all_emails = service.list_sent_emails(
        from_addr=sender_email,
        include_recalled=True,  # 包含已撤回邮件
        limit=10
    )
    
    found_sent_with_recalled = any(email["message_id"] == test_message_id for email in sender_all_emails)
    print(f"📤 发件人已发送（包含已撤回）: {'✅ 找到' if found_sent_with_recalled else '❌ 未找到'}")
    
    # 8. 检查撤回状态
    print("\n8️⃣ 检查撤回状态...")
    if found_with_recalled:
        recalled_email = next(email for email in recipient_all_emails if email["message_id"] == test_message_id)
        is_recalled = recalled_email.get("is_recalled", False)
        recalled_at = recalled_email.get("recalled_at", "未知")
        recalled_by = recalled_email.get("recalled_by", "未知")
        
        print(f"📋 邮件撤回状态: {'✅ 已撤回' if is_recalled else '❌ 未撤回'}")
        print(f"📅 撤回时间: {recalled_at}")
        print(f"👤 撤回者: {recalled_by}")
    
    # 9. 清理测试数据
    print("\n9️⃣ 清理测试数据...")
    try:
        service.delete_email_metadata(test_message_id)
        service.delete_sent_email_metadata(test_message_id)
        print("✅ 测试数据已清理")
    except Exception as e:
        print(f"⚠️ 清理测试数据时出错: {e}")
    
    # 10. 测试结果
    print("\n" + "=" * 60)
    print("📊 完整流程测试结果")
    print("=" * 60)
    
    success = (
        found_before_recall and found_sent_before_recall and  # 撤回前可见
        not found_after_recall and not found_sent_after_recall and  # 撤回后不可见
        found_with_recalled and found_sent_with_recalled  # include_recalled=True时可见
    )
    
    if success:
        print("🎉 完整邮件撤回流程测试通过！")
        print("✅ 邮件发送和保存正常")
        print("✅ 撤回前收件人和发件人都能看到邮件")
        print("✅ 撤回操作成功执行")
        print("✅ 撤回后收件人和发件人都看不到邮件")
        print("✅ POP3服务器正确过滤已撤回邮件")
        print("✅ CLI正确过滤已撤回邮件")
        print("✅ 管理员模式可以查看已撤回邮件")
        print("\n💡 这意味着:")
        print("   - 如果邮件还没被收件人下载，撤回后收件人将无法看到")
        print("   - 如果邮件已被下载到本地，撤回无法删除本地副本")
        print("   - 撤回功能在服务器端正确工作")
    else:
        print("❌ 完整邮件撤回流程测试失败！")
        if not found_before_recall or not found_sent_before_recall:
            print("❌ 邮件发送或保存失败")
        if found_after_recall:
            print("❌ 撤回后收件人仍能看到邮件")
        if found_sent_after_recall:
            print("❌ 撤回后发件人仍能看到邮件")
        if not found_with_recalled or not found_sent_with_recalled:
            print("❌ include_recalled=True时无法查看已撤回邮件")
    
    return success

def main():
    """主函数"""
    try:
        success = test_complete_recall_flow()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
