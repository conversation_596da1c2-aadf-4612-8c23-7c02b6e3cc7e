#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断邮件撤回问题
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

def diagnose_recall():
    """诊断邮件撤回问题"""
    print("🔍 诊断邮件撤回问题")
    print("=" * 50)
    
    try:
        from server.new_db_handler import EmailService
        service = EmailService()
        user_email = "<EMAIL>"
        
        print(f"📧 检查用户: {user_email}")
        
        # 1. 检查API是否支持include_recalled参数
        print("\n1️⃣ 检查API支持...")
        try:
            # 测试list_emails
            emails = service.list_emails(user_email=user_email, include_recalled=False, limit=1)
            print("✅ list_emails支持include_recalled参数")
        except Exception as e:
            print(f"❌ list_emails不支持include_recalled参数: {e}")
            return
        
        try:
            # 测试list_sent_emails
            sent_emails = service.list_sent_emails(from_addr=user_email, include_recalled=False, limit=1)
            print("✅ list_sent_emails支持include_recalled参数")
        except Exception as e:
            print(f"❌ list_sent_emails不支持include_recalled参数: {e}")
            return
        
        # 2. 检查数据库中的撤回状态
        print("\n2️⃣ 检查数据库撤回状态...")
        
        # 直接查询数据库
        try:
            import sqlite3
            db_path = "data/email_db.sqlite"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询收件箱中的撤回邮件
            cursor.execute("""
                SELECT message_id, subject, is_recalled, recalled_at, recalled_by 
                FROM emails 
                WHERE (to_addrs LIKE ? OR from_addr = ?) 
                AND is_recalled = 1
                LIMIT 5
            """, (f'%{user_email}%', user_email))
            
            recalled_emails = cursor.fetchall()
            print(f"📥 收件箱中已撤回邮件数: {len(recalled_emails)}")
            
            for email in recalled_emails:
                message_id, subject, is_recalled, recalled_at, recalled_by = email
                print(f"  - {subject[:30]} (撤回者: {recalled_by}, 时间: {recalled_at})")
            
            # 查询已发送邮件中的撤回邮件
            cursor.execute("""
                SELECT message_id, subject, is_recalled, recalled_at, recalled_by 
                FROM sent_emails 
                WHERE from_addr = ? 
                AND is_recalled = 1
                LIMIT 5
            """, (user_email,))
            
            sent_recalled_emails = cursor.fetchall()
            print(f"📤 已发送中已撤回邮件数: {len(sent_recalled_emails)}")
            
            for email in sent_recalled_emails:
                message_id, subject, is_recalled, recalled_at, recalled_by = email
                print(f"  - {subject[:30]} (撤回者: {recalled_by}, 时间: {recalled_at})")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
            return
        
        # 3. 测试API过滤效果
        print("\n3️⃣ 测试API过滤效果...")
        
        # 获取所有邮件（包含已撤回）
        all_emails = service.list_emails(user_email=user_email, include_recalled=True, limit=10)
        print(f"📊 所有邮件数（包含已撤回）: {len(all_emails)}")
        
        # 获取正常邮件（不包含已撤回）
        normal_emails = service.list_emails(user_email=user_email, include_recalled=False, limit=10)
        print(f"📊 正常邮件数（不包含已撤回）: {len(normal_emails)}")
        
        # 计算差异
        recalled_count = len(all_emails) - len(normal_emails)
        print(f"📊 API过滤出的已撤回邮件数: {recalled_count}")
        
        # 4. 分析问题
        print("\n4️⃣ 问题分析...")
        
        if len(recalled_emails) == 0 and len(sent_recalled_emails) == 0:
            print("❌ 数据库中没有已撤回邮件")
            print("💡 可能的原因:")
            print("   1. 撤回操作失败了")
            print("   2. 撤回的邮件被删除了")
            print("   3. 撤回状态没有正确保存")
        elif recalled_count == 0:
            print("❌ API过滤没有生效")
            print("💡 可能的原因:")
            print("   1. CLI查询时没有使用include_recalled=False参数")
            print("   2. 数据库查询逻辑有问题")
        else:
            print("✅ 撤回功能正常工作")
            print("💡 如果CLI仍显示已撤回邮件，可能是:")
            print("   1. CLI缓存了旧的邮件列表")
            print("   2. CLI查询参数不正确")
        
        # 5. 建议解决方案
        print("\n5️⃣ 建议解决方案...")
        
        if recalled_count > 0:
            print("✅ 撤回功能已修复并正常工作")
            print("💡 如果CLI仍显示已撤回邮件，请:")
            print("   1. 重启CLI程序")
            print("   2. 重新查看收件箱")
            print("   3. 检查是否选择了正确的过滤选项")
        else:
            print("❌ 需要进一步调试")
            print("💡 请检查:")
            print("   1. 撤回操作是否真的成功了")
            print("   2. 数据库是否正确更新了")
            print("   3. CLI是否使用了修复后的代码")
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_recall()
