#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试撤回功能修复 - 针对收件箱中的邮件
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

def test_recall_inbox_email():
    """测试撤回收件箱中的邮件"""
    print("🔧 测试撤回收件箱邮件功能")
    print("=" * 50)
    
    try:
        from server.new_db_handler import EmailService
        service = EmailService()
        
        user_email = "<EMAIL>"
        
        # 1. 获取当前收件箱中的撤回测试邮件
        print("1️⃣ 获取撤回测试邮件...")
        emails = service.list_emails(
            user_email=user_email,
            include_recalled=True,  # 包含已撤回邮件
            limit=10
        )
        
        test_emails = [email for email in emails if "撤回测试" in email.get("subject", "")]
        print(f"📧 找到撤回测试邮件: {len(test_emails)}封")
        
        if not test_emails:
            print("❌ 没有找到撤回测试邮件")
            return False
        
        # 选择最新的一封邮件进行测试
        latest_email = test_emails[0]
        message_id = latest_email["message_id"]
        subject = latest_email["subject"]
        
        print(f"📧 选择邮件: {subject}")
        print(f"📧 Message-ID: {message_id}")
        
        # 2. 检查撤回权限
        print("\n2️⃣ 检查撤回权限...")
        permission = service.email_repo.can_recall_email(message_id, user_email)
        
        print(f"🔍 撤回权限: {'✅ 可以撤回' if permission['can_recall'] else '❌ 不能撤回'}")
        print(f"📝 原因: {permission['reason']}")
        
        if not permission["can_recall"]:
            print("❌ 无法撤回该邮件")
            return False
        
        # 3. 执行撤回操作
        print("\n3️⃣ 执行撤回操作...")
        recall_result = service.recall_email(message_id, user_email)
        
        if recall_result["success"]:
            print(f"✅ 撤回成功: {recall_result['message']}")
        else:
            print(f"❌ 撤回失败: {recall_result['message']}")
            return False
        
        # 4. 验证撤回状态
        print("\n4️⃣ 验证撤回状态...")
        
        # 检查数据库中的撤回状态
        import sqlite3
        db_path = "data/email_db.sqlite"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT is_recalled, recalled_at, recalled_by 
            FROM emails 
            WHERE message_id = ?
        """, (message_id,))
        
        result = cursor.fetchone()
        if result:
            is_recalled, recalled_at, recalled_by = result
            print(f"📊 数据库撤回状态: is_recalled={is_recalled}")
            print(f"📅 撤回时间: {recalled_at}")
            print(f"👤 撤回者: {recalled_by}")
            
            if is_recalled == 1:
                print("✅ 数据库状态更新成功")
            else:
                print("❌ 数据库状态未更新")
                conn.close()
                return False
        else:
            print("❌ 在数据库中未找到邮件")
            conn.close()
            return False
        
        conn.close()
        
        # 5. 测试API过滤效果
        print("\n5️⃣ 测试API过滤效果...")
        
        # 不包含已撤回邮件的查询
        emails_normal = service.list_emails(
            user_email=user_email,
            include_recalled=False,
            limit=10
        )
        
        found_in_normal = any(email["message_id"] == message_id for email in emails_normal)
        print(f"📥 正常查询中找到: {'❌ 是（错误）' if found_in_normal else '✅ 否（正确）'}")
        
        # 包含已撤回邮件的查询
        emails_all = service.list_emails(
            user_email=user_email,
            include_recalled=True,
            limit=10
        )
        
        found_in_all = any(email["message_id"] == message_id for email in emails_all)
        print(f"📥 完整查询中找到: {'✅ 是' if found_in_all else '❌ 否（错误）'}")
        
        # 检查撤回状态
        if found_in_all:
            recalled_email = next(email for email in emails_all if email["message_id"] == message_id)
            api_is_recalled = recalled_email.get("is_recalled", False)
            print(f"📊 API返回的撤回状态: {'✅ 已撤回' if api_is_recalled else '❌ 未撤回'}")
        
        # 6. 测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果")
        print("=" * 50)
        
        success = (
            is_recalled == 1 and  # 数据库状态正确
            not found_in_normal and  # 正常查询中被过滤
            found_in_all  # 完整查询中可见
        )
        
        if success:
            print("🎉 撤回功能修复成功！")
            print("✅ 撤回操作正常执行")
            print("✅ 数据库状态正确更新")
            print("✅ API过滤正确生效")
            print("\n💡 现在你可以:")
            print("   1. 重启CLI程序")
            print("   2. 查看收件箱，撤回的邮件应该不再显示")
            print("   3. 在撤回菜单中，该邮件应该不再出现在可撤回列表中")
        else:
            print("❌ 撤回功能仍有问题")
            if is_recalled != 1:
                print("❌ 数据库状态未正确更新")
            if found_in_normal:
                print("❌ API过滤未生效")
            if not found_in_all:
                print("❌ 完整查询无法找到邮件")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_recall_inbox_email()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
