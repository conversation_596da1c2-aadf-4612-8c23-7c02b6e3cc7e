#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件撤回功能修复测试脚本

测试邮件撤回功能是否正常工作：
1. 发送测试邮件
2. 撤回邮件
3. 验证撤回后邮件是否被正确隐藏
"""

import os
import sys
import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent))

from common.utils import setup_logging
from server.new_db_handler import EmailService
from common.models import Email, EmailAddress

# 设置日志
logger = setup_logging("test_email_recall")


def test_email_recall_functionality():
    """测试邮件撤回功能"""
    print("=" * 60)
    print("🔙 邮件撤回功能测试")
    print("=" * 60)

    # 初始化邮件服务
    email_service = EmailService()

    # 测试用户邮箱
    test_user = "<EMAIL>"
    test_recipient = "<EMAIL>"

    print(f"📧 测试用户: {test_user}")
    print(f"📨 测试收件人: {test_recipient}")

    # 1. 创建测试邮件
    print("\n1️⃣ 创建测试邮件...")
    test_email = Email(
        message_id=f"test-recall-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}@test.local",
        subject="测试撤回功能的邮件",
        from_addr=EmailAddress("测试发件人", test_user),
        to_addrs=[EmailAddress("测试收件人", test_recipient)],
        cc_addrs=[],
        bcc_addrs=[],
        date=datetime.datetime.now(),
        text_content="这是一封用于测试撤回功能的邮件。如果撤回成功，收件人应该看不到这封邮件。",
        html_content="",
        attachments=[],
    )

    # 2. 保存邮件到数据库（模拟接收邮件）
    print("2️⃣ 保存邮件到数据库...")
    try:
        # 保存到收件箱 - 使用正确的API
        email_service.save_email_metadata(
            message_id=test_email.message_id,
            from_addr=test_email.from_addr.address,
            to_addrs=[addr.address for addr in test_email.to_addrs],
            subject=test_email.subject,
            date=test_email.date,
            size=len(test_email.text_content),
        )
        print(f"✅ 邮件已保存到收件箱: {test_email.message_id}")

        # 保存到已发送邮件 - 创建已发送邮件记录
        from server.db_models import SentEmailRecord

        sent_record = SentEmailRecord(
            message_id=test_email.message_id,
            from_addr=test_email.from_addr.address,
            to_addrs=[addr.address for addr in test_email.to_addrs],
            cc_addrs=[],
            bcc_addrs=[],
            subject=test_email.subject,
            date=test_email.date,
            size=len(test_email.text_content),
        )
        success = email_service.email_repo.create_sent_email(sent_record)
        if success:
            print(f"✅ 邮件已保存到已发送: {test_email.message_id}")
        else:
            print("❌ 保存邮件到已发送失败")
            return False

    except Exception as e:
        print(f"❌ 保存邮件时出错: {e}")
        return False

    # 3. 验证邮件在撤回前可见
    print("\n3️⃣ 验证邮件在撤回前可见...")

    # 检查收件人能看到邮件
    received_emails = email_service.list_emails(
        user_email=test_recipient, include_recalled=False  # 默认不包含已撤回邮件
    )

    found_in_inbox = any(
        email["message_id"] == test_email.message_id for email in received_emails
    )
    print(f"📥 收件箱中找到邮件: {'✅ 是' if found_in_inbox else '❌ 否'}")

    # 检查发件人能看到已发送邮件
    sent_emails = email_service.list_sent_emails(
        from_addr=test_user, include_recalled=False  # 默认不包含已撤回邮件
    )

    found_in_sent = any(
        email["message_id"] == test_email.message_id for email in sent_emails
    )
    print(f"📤 已发送中找到邮件: {'✅ 是' if found_in_sent else '❌ 否'}")

    if not found_in_inbox or not found_in_sent:
        print("❌ 邮件在撤回前不可见，测试失败")
        return False

    # 4. 撤回邮件
    print("\n4️⃣ 撤回邮件...")
    try:
        recall_result = email_service.recall_email(test_email.message_id, test_user)
        if recall_result["success"]:
            print(f"✅ 邮件撤回成功: {recall_result['message']}")
        else:
            print(f"❌ 邮件撤回失败: {recall_result['message']}")
            return False
    except Exception as e:
        print(f"❌ 撤回邮件时出错: {e}")
        return False

    # 5. 验证邮件在撤回后被隐藏
    print("\n5️⃣ 验证邮件在撤回后被隐藏...")

    # 检查收件人看不到邮件（include_recalled=False）
    received_emails_after = email_service.list_emails(
        user_email=test_recipient, include_recalled=False  # 不包含已撤回邮件
    )

    found_in_inbox_after = any(
        email["message_id"] == test_email.message_id for email in received_emails_after
    )
    print(
        f"📥 收件箱中找到邮件（撤回后）: {'❌ 是（错误）' if found_in_inbox_after else '✅ 否（正确）'}"
    )

    # 检查发件人看不到已发送邮件（include_recalled=False）
    sent_emails_after = email_service.list_sent_emails(
        from_addr=test_user, include_recalled=False  # 不包含已撤回邮件
    )

    found_in_sent_after = any(
        email["message_id"] == test_email.message_id for email in sent_emails_after
    )
    print(
        f"📤 已发送中找到邮件（撤回后）: {'❌ 是（错误）' if found_in_sent_after else '✅ 否（正确）'}"
    )

    # 6. 验证使用include_recalled=True时能看到已撤回邮件
    print("\n6️⃣ 验证include_recalled=True时能看到已撤回邮件...")

    # 检查收件人使用include_recalled=True能看到邮件
    received_emails_with_recalled = email_service.list_emails(
        user_email=test_recipient, include_recalled=True  # 包含已撤回邮件
    )

    found_with_recalled = any(
        email["message_id"] == test_email.message_id
        for email in received_emails_with_recalled
    )
    print(
        f"📥 收件箱中找到邮件（include_recalled=True）: {'✅ 是' if found_with_recalled else '❌ 否'}"
    )

    # 检查已撤回状态
    if found_with_recalled:
        recalled_email = next(
            email
            for email in received_emails_with_recalled
            if email["message_id"] == test_email.message_id
        )
        is_recalled = recalled_email.get("is_recalled", False)
        print(f"📋 邮件撤回状态: {'✅ 已撤回' if is_recalled else '❌ 未撤回'}")

    # 7. 清理测试数据
    print("\n7️⃣ 清理测试数据...")
    try:
        # 删除测试邮件
        email_service.delete_email_metadata(test_email.message_id)
        email_service.delete_sent_email_metadata(test_email.message_id)
        print("✅ 测试数据已清理")
    except Exception as e:
        print(f"⚠️ 清理测试数据时出错: {e}")

    # 8. 测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)

    success = (
        found_in_inbox
        and found_in_sent  # 撤回前可见
        and not found_in_inbox_after
        and not found_in_sent_after  # 撤回后不可见
        and found_with_recalled  # include_recalled=True时可见
    )

    if success:
        print("🎉 邮件撤回功能测试通过！")
        print("✅ 撤回前邮件可见")
        print("✅ 撤回后邮件被正确隐藏")
        print("✅ include_recalled=True时可以查看已撤回邮件")
    else:
        print("❌ 邮件撤回功能测试失败！")
        if found_in_inbox_after or found_in_sent_after:
            print("❌ 撤回后邮件仍然可见")
        if not found_with_recalled:
            print("❌ include_recalled=True时无法查看已撤回邮件")

    return success


def main():
    """主函数"""
    try:
        success = test_email_recall_functionality()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        print(f"❌ 测试过程中出现异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
