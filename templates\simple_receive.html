{% extends "base.html" %} {% block title %}接收邮件 - 邮件客户端{% endblock %}
{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h3 class="card-title mb-0"><i class="fas fa-inbox"></i> 接收邮件</h3>
          <div class="d-flex align-items-center gap-3">
            <!-- 邮件数量选择 -->
            <div class="d-flex align-items-center">
              <label for="emailLimit" class="form-label me-2 mb-0">
                <small>显示数量:</small>
              </label>
              <select
                class="form-select form-select-sm"
                id="emailLimit"
                style="width: auto"
              >
                <option value="10">10封</option>
                <option value="20" selected>20封</option>
                <option value="50">50封</option>
                <option value="100">100封</option>
                <option value="200">200封</option>
                <option value="all">全部</option>
              </select>
            </div>

            <!-- 刷新按钮 -->
            <button
              class="btn btn-outline-primary"
              onclick="refreshEmails()"
              id="refreshBtn"
            >
              <i class="fas fa-sync-alt"></i> 刷新邮件
            </button>
          </div>
        </div>

        {% if account %}
        <div class="alert alert-info">
          <i class="fas fa-user"></i> 当前账户:
          <strong>{{ account.email }}</strong>
        </div>
        {% endif %} {% if emails %}
        <div class="table-responsive">
          <table class="table table-hover">
            <thead class="table-light">
              <tr>
                <th style="width: 5%">#</th>
                <th style="width: 20%">发件人</th>
                <th style="width: 30%">主题</th>
                <th style="width: 15%">日期</th>
                <th style="width: 10%">大小</th>
                <th style="width: 20%">操作</th>
              </tr>
            </thead>
            <tbody>
              {% for email in emails %}
              <tr>
                <td>{{ loop.index }}</td>
                <td>
                  <div class="d-flex align-items-center">
                    <i class="fas fa-envelope-open text-primary me-2"></i>
                    <div>
                      <strong
                        >{{ email.from_addr.name or email.from_addr.address
                        }}</strong
                      >
                      {% if email.from_addr.name %}
                      <br /><small class="text-muted"
                        >{{ email.from_addr.address }}</small
                      >
                      {% endif %}
                    </div>
                  </div>
                </td>
                <td>
                  <div>
                    <strong>{{ email.subject or '(无主题)' }}</strong>
                    {% if email.attachments %}
                    <i
                      class="fas fa-paperclip text-success ms-1"
                      title="有附件"
                    ></i>
                    {% endif %}
                  </div>
                  {% if email.text_content %}
                  <small class="text-muted">
                    {{ email.text_content[:80] }}{% if email.text_content|length
                    > 80 %}...{% endif %}
                  </small>
                  {% endif %}
                </td>
                <td>
                  <small>
                    {{ email.formatted_date or email.date }}<br />
                    {{ email.formatted_time or '' }}
                  </small>
                </td>
                <td>
                  <small class="text-muted">
                    {% set content_size = (email.text_content|length if
                    email.text_content else 0) + (email.html_content|length if
                    email.html_content else 0) %} {% if content_size < 1024 %}
                    {{ content_size }} B {% elif content_size < 1048576 %} {{
                    "%.1f"|format(content_size / 1024) }} KB {% else %} {{
                    "%.1f"|format(content_size / 1048576) }} MB {% endif %}
                  </small>
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <button
                      class="btn btn-sm btn-outline-primary view-email-btn"
                      data-email-index="{{ loop.index0 }}"
                      title="查看邮件"
                    >
                      <i class="fas fa-eye"></i>
                    </button>
                    <button
                      class="btn btn-sm btn-outline-danger delete-email-btn"
                      data-email-index="{{ loop.index0 }}"
                      data-message-id="{{ email.message_id }}"
                      title="删除邮件"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <div class="mt-3">
          <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
              <i class="fas fa-info-circle"></i>
              {% if current_limit == 'all' %} 显示全部 {{ emails|length }}
              封邮件 {% else %} 显示最新 {{ emails|length }} 封邮件（限制: {{
              current_limit }} 封） {% endif %}
            </small>

            {% if emails|length >= 20 %}
            <small class="text-muted">
              <i class="fas fa-lightbulb"></i>
              提示：如需查看更多邮件，请调整上方的显示数量
            </small>
            {% endif %}
          </div>

          <!-- 性能提示 -->
          {% if current_limit == 'all' or (current_limit|int) >= 100 %}
          <div class="alert alert-info mt-2 py-2">
            <small>
              <i class="fas fa-clock"></i>
              {% if current_limit == 'all' %}
              正在显示所有邮件，如果邮件数量很多可能需要较长加载时间 {% else %}
              大量邮件可能需要较长加载时间，建议选择较小的数量以获得更好的体验
              {% endif %}
            </small>
          </div>
          {% endif %}
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">暂无邮件</h5>
          <p class="text-muted">点击上方刷新按钮获取最新邮件</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- 邮件详情模态框 -->
<div
  class="modal fade"
  id="emailModal"
  tabindex="-1"
  aria-labelledby="emailModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="emailModalLabel">
          <i class="fas fa-envelope-open"></i> 邮件详情
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div id="emailContent">
          <!-- 邮件内容将通过JavaScript插入 -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          关闭
        </button>
        <button type="button" class="btn btn-primary" onclick="replyEmail()">
          <i class="fas fa-reply"></i> 回复
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 使用提示 -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <h5><i class="fas fa-lightbulb"></i> 使用提示</h5>
        <div class="row">
          <div class="col-md-4">
            <div class="text-center">
              <i class="fas fa-sync-alt fa-2x text-primary mb-2"></i>
              <h6>实时获取</h6>
              <p class="text-muted small">点击刷新按钮获取最新邮件</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="text-center">
              <i class="fas fa-eye fa-2x text-success mb-2"></i>
              <h6>查看详情</h6>
              <p class="text-muted small">点击查看按钮阅读完整邮件</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="text-center">
              <i class="fas fa-shield-alt fa-2x text-info mb-2"></i>
              <h6>基于CLI</h6>
              <p class="text-muted small">使用CLI底层POP3客户端</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %} {% block scripts %}
<script>
  // 全局邮件数据
  window.emailsData = {{ emails | tojson | safe if emails else "[]" }};
  window.currentLimit = "{{ current_limit or '20' }}";

  console.log("页面加载，邮件数据:", window.emailsData.length, "封");

  // 页面初始化
  document.addEventListener("DOMContentLoaded", function() {
    console.log("DOM加载完成");

    // 设置邮件数量选择框
    const limitSelect = document.getElementById("emailLimit");
    if (limitSelect) {
      limitSelect.value = window.currentLimit;
      console.log("设置邮件数量限制:", window.currentLimit);

      // 监听选择框变化
      limitSelect.addEventListener("change", function() {
        console.log("用户更改邮件数量限制为:", this.value);
        setTimeout(function() {
          refreshEmails();
        }, 300);
      });
    }

    // 添加按钮点击事件监听器
    document.addEventListener("click", function(event) {
      // 查看邮件按钮
      if (event.target.closest(".view-email-btn")) {
        console.log("查看邮件按钮被点击");
        const button = event.target.closest(".view-email-btn");
        const emailIndex = parseInt(button.getAttribute("data-email-index"));
        console.log("邮件索引:", emailIndex);
        viewEmail(emailIndex);
        return;
      }

      // 删除邮件按钮
      if (event.target.closest(".delete-email-btn")) {
        console.log("删除邮件按钮被点击");
        const button = event.target.closest(".delete-email-btn");
        const emailIndex = parseInt(button.getAttribute("data-email-index"));
        const messageId = button.getAttribute("data-message-id");
        console.log("邮件索引:", emailIndex, "消息ID:", messageId);
        deleteEmail(emailIndex, messageId);
        return;
      }
    });

    console.log("页面初始化完成");
  });

  // 刷新邮件
  function refreshEmails() {
    console.log("刷新邮件");
    const limitSelect = document.getElementById("emailLimit");
    const refreshBtn = document.getElementById("refreshBtn");

    if (!limitSelect || !refreshBtn) {
      console.error("页面元素缺失");
      alert("页面元素缺失，请刷新页面");
      return;
    }

    const selectedLimit = limitSelect.value;
    console.log("选择的邮件数量限制:", selectedLimit);

    // 显示加载状态
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 获取中...';
    refreshBtn.disabled = true;

    // 跳转
    const url = window.location.pathname + "?limit=" + encodeURIComponent(selectedLimit);
    console.log("跳转到:", url);
    window.location.href = url;
  }

  // 查看邮件
  function viewEmail(index) {
    console.log("查看邮件，索引:", index);

    if (!window.emailsData || !Array.isArray(window.emailsData)) {
      console.error("邮件数据异常");
      alert("邮件数据异常，请刷新页面");
      return;
    }

    if (index < 0 || index >= window.emailsData.length) {
      console.error("邮件索引无效:", index);
      alert("邮件索引无效: " + index);
      return;
    }

    const email = window.emailsData[index];
    console.log("要查看的邮件:", email);

    if (!email) {
      console.error("邮件数据为空");
      alert("邮件数据为空");
      return;
    }

    try {
      // 构建邮件内容
      let emailHtml = '<div class="mb-3">';
      emailHtml += '<strong>发件人:</strong> ';

      if (email.from_addr && email.from_addr.address) {
        emailHtml += email.from_addr.name || email.from_addr.address;
      } else {
        emailHtml += '未知发件人';
      }

      emailHtml += '</div>';
      emailHtml += '<div class="mb-3"><strong>主题:</strong> ' + (email.subject || "(无主题)") + '</div>';
      emailHtml += '<div class="mb-3"><strong>日期:</strong> ' + (email.formatted_datetime || email.date || "未知日期") + '</div>';
      emailHtml += '<hr><div class="mb-3"><strong>邮件内容:</strong></div>';

      if (email.text_content) {
        emailHtml += '<div class="border p-3"><pre style="white-space: pre-wrap; font-family: inherit;">';
        emailHtml += email.text_content;
        emailHtml += '</pre></div>';
      } else if (email.html_content) {
        emailHtml += '<div class="border p-3">' + email.html_content + '</div>';
      } else {
        emailHtml += '<div class="text-muted">无邮件内容</div>';
      }

      // 设置模态框内容
      const emailContentElement = document.getElementById("emailContent");
      const emailModalLabelElement = document.getElementById("emailModalLabel");

      if (!emailContentElement || !emailModalLabelElement) {
        console.error("模态框元素未找到");
        alert("模态框元素未找到");
        return;
      }

      emailContentElement.innerHTML = emailHtml;
      emailModalLabelElement.innerHTML = '<i class="fas fa-envelope-open"></i> ' + (email.subject || "(无主题)");

      // 显示模态框
      const emailModalElement = document.getElementById("emailModal");
      if (emailModalElement && typeof bootstrap !== 'undefined') {
        const modal = new bootstrap.Modal(emailModalElement);
        modal.show();
        console.log("模态框已显示");
      } else {
        console.error("Bootstrap或模态框元素未找到");
        alert("无法显示邮件详情");
      }

    } catch (error) {
      console.error("显示邮件详情时出错:", error);
      alert("显示邮件详情时出错: " + error.message);
    }
  }

  // 更新邮件显示
  function updateEmailDisplay() {
    console.log("更新邮件显示，当前邮件数量:", window.emailsData.length);

    const emailTableBody = document.querySelector("table tbody");
    if (!emailTableBody) {
      console.error("邮件表格tbody元素未找到");
      return;
    }

    if (window.emailsData.length === 0) {
      // 如果没有邮件，显示空状态
      const emailContainer = document.querySelector(".table-responsive").parentElement;
      emailContainer.innerHTML = `
        <div class="text-center py-5">
          <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">暂无邮件</h5>
          <p class="text-muted">点击上方刷新按钮获取最新邮件</p>
        </div>
      `;
      return;
    }

    let html = '';
    window.emailsData.forEach(function(email, index) {
      const fromAddr = email.from_addr && email.from_addr.address ? email.from_addr.address : "未知发件人";
      const fromName = email.from_addr && email.from_addr.name ? email.from_addr.name : fromAddr;
      const subject = email.subject || "(无主题)";
      const date = email.formatted_date || email.date || "未知日期";
      const time = email.formatted_time || "";
      const messageId = email.message_id || "";

      // 计算邮件大小
      const contentSize = (email.text_content ? email.text_content.length : 0) +
                         (email.html_content ? email.html_content.length : 0);
      let sizeText = "";
      if (contentSize < 1024) {
        sizeText = contentSize + " B";
      } else if (contentSize < 1048576) {
        sizeText = (contentSize / 1024).toFixed(1) + " KB";
      } else {
        sizeText = (contentSize / 1048576).toFixed(1) + " MB";
      }

      html += `
        <tr>
          <td>${index + 1}</td>
          <td>
            <div class="d-flex align-items-center">
              <i class="fas fa-envelope-open text-primary me-2"></i>
              <div>
                <strong>${fromName}</strong>
                ${fromName !== fromAddr ? '<br><small class="text-muted">' + fromAddr + '</small>' : ''}
              </div>
            </div>
          </td>
          <td>
            <div>
              <strong>${subject}</strong>
              ${email.attachments && email.attachments.length > 0 ? '<i class="fas fa-paperclip text-success ms-1" title="有附件"></i>' : ''}
            </div>
            ${email.text_content ? '<small class="text-muted">' + (email.text_content.substring(0, 80) + (email.text_content.length > 80 ? '...' : '')) + '</small>' : ''}
          </td>
          <td>
            <small>
              ${date}<br>
              ${time}
            </small>
          </td>
          <td>
            <small class="text-muted">
              ${sizeText}
            </small>
          </td>
          <td>
            <div class="btn-group" role="group">
              <button
                class="btn btn-sm btn-outline-primary view-email-btn"
                data-email-index="${index}"
                title="查看邮件"
              >
                <i class="fas fa-eye"></i>
              </button>
              <button
                class="btn btn-sm btn-outline-danger delete-email-btn"
                data-email-index="${index}"
                data-message-id="${messageId}"
                title="删除邮件"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      `;
    });

    emailTableBody.innerHTML = html;
    console.log("邮件表格显示已更新");
  }

  // 删除邮件
  function deleteEmail(index, messageId) {
    console.log("删除邮件:", index, messageId);

    if (!window.emailsData || !Array.isArray(window.emailsData)) {
      console.error("邮件数据异常");
      alert("邮件数据异常，请刷新页面");
      return;
    }

    if (index < 0 || index >= window.emailsData.length) {
      console.error("邮件索引无效:", index);
      alert("邮件索引无效: " + index);
      return;
    }

    const email = window.emailsData[index];
    if (!email) {
      console.error("邮件数据为空");
      alert("邮件数据为空");
      return;
    }

    const subject = email.subject || "(无主题)";
    const fromAddr = (email.from_addr && email.from_addr.address) ? email.from_addr.address : "未知发件人";

    // 确认删除
    if (!confirm("确定要删除这封邮件吗？\n\n主题: " + subject + "\n发件人: " + fromAddr)) {
      console.log("用户取消删除");
      return;
    }

    console.log("开始删除邮件");

    // 发送删除请求
    fetch("/api/delete_email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message_id: messageId,
        email_type: "received",
      }),
    })
    .then(function(response) {
      console.log("删除请求响应状态:", response.status);
      if (!response.ok) {
        throw new Error("HTTP error! status: " + response.status);
      }
      return response.json();
    })
    .then(function(data) {
      console.log("删除响应数据:", data);
      if (data.success) {
        alert("邮件删除成功！");

        // 从前端数据中移除该邮件
        window.emailsData.splice(index, 1);

        // 更新界面显示
        updateEmailDisplay();

        console.log("邮件已从界面中移除，剩余邮件数量:", window.emailsData.length);
      } else {
        alert("删除失败: " + data.error);
      }
    })
    .catch(function(error) {
      console.error("删除邮件时出错:", error);
      alert("删除邮件时出错: " + error.message);
    });
  }

  // 回复邮件
  function replyEmail() {
    console.log("回复邮件功能暂未实现");
    alert("回复邮件功能暂未实现");
  }
</script>
{% endblock %}
