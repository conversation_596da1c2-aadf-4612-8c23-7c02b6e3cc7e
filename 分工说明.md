# 项目分工与贡献说明

## 一、 项目概述与团队角色

本项目旨在实现一个功能完整的邮件系统。在开发过程中，团队成员紧密合作，各司其职，共同推进了项目的顺利完成。为明确各成员的职责与贡献，特此说明。

**团队角色定义如下：**

| 姓名       | 角色定位               | 主要职责                                       |
| :--------- | :--------------------- | :--------------------------------------------- |
| **林纪帆** | **核心开发和架构设计** | 负责项目整体架构、核心功能开发与最终代码集成。 |
| **康铁瀚** | **扩展功能开发**       | 负责"垃圾邮件过滤"模块的开发。                 |
| **吴柯**   | **扩展功能开发**       | 负责"PGP端到端加密"模块的开发。                |
| **邓熙涵** | **系统测试与文档支持** | 协助进行系统功能和并发性能测试、安全验证。     |
| **宋国豪** | **团队协作与资料整理** | 负责团队沟通协调及项目资料的收集整理工作。     |

---

## 二、 详细任务分工

### 1. 架构设计与核心功能开发

**主要负责人：林纪帆**

- **项目架构设计**：独立设计搭建了项目的整体技术架构，包括客户端、服务端和数据库的交互模式。
- **服务端开发**：独立完成了SMTP/POP3服务器的全部基础功能，包括：
    - 协议的底层实现
    - 高并发连接处理
    - 数据库交互与邮件存储（SQLite）
- **客户端开发**：独立完成了客户端的全部基础功能，包括：
    - 邮件发送（SMTP）与接收（POP3）
    - MIME协议支持（附件、HTML邮件）
    - 用户认证与本地存储
- **核心扩展功能**：主导并实现了 **"邮件撤回"** 与 **"Web邮件界面"** 功能。
- **代码集成与优化**：负责将各扩展功能模块集成到主系统中，并确保整体稳定性。

### 2. 扩展功能模块开发

- **垃圾邮件过滤模块**：由 **康铁瀚** 负责完成此模块的主体开发。
- **PGP端到端加密模块**：由 **吴柯** 负责完成此模块的主体开发。

### 3. 测试与质量保障

- **并发压力测试**：由 **林纪帆** 和 **邓熙涵** 共同完成测试脚本编写、执行与报告撰写。
- **功能演示准备**：
    - **附件功能演示**：由 **林纪帆** 准备并完成。
    - **SSL安全演示**：由 **林纪帆** 和 **邓熙涵** 共同准备并完成。

### 4. 文档与演示材料

- **设计文档**（协议流程图、MIME结构、SSL握手过程）：由 **林纪帆** 完成。
- **学术海报 (Poster)**：由 **林纪帆, 康铁瀚** 设计与制作。
- **用户手册**：待分配。

---

## 三、 总结

本次大作业的成功离不开每位成员的努力。林纪帆作为核心开发，承担了项目架构设计与主要功能实现；康铁瀚和吴柯分别在扩展功能上做出了重要贡献；邓熙涵在测试和文档方面提供了关键支持；宋国豪也参与了团队的日常协作。整个团队在协作中不断磨合，共同成长。 