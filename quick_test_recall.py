#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试邮件撤回修复
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

from server.new_db_handler import EmailService

def quick_test():
    """快速测试邮件撤回功能"""
    print("🔙 快速测试邮件撤回修复")
    
    # 初始化服务
    service = EmailService()
    
    # 测试参数
    test_user = "<EMAIL>"
    
    print(f"📧 测试用户: {test_user}")
    
    # 1. 测试list_emails是否支持include_recalled参数
    print("\n1️⃣ 测试list_emails参数...")
    try:
        emails = service.list_emails(
            user_email=test_user,
            include_recalled=False,
            limit=5
        )
        print(f"✅ list_emails支持include_recalled参数，返回{len(emails)}封邮件")
    except Exception as e:
        print(f"❌ list_emails不支持include_recalled参数: {e}")
        return False
    
    # 2. 测试list_sent_emails是否支持include_recalled参数
    print("\n2️⃣ 测试list_sent_emails参数...")
    try:
        sent_emails = service.list_sent_emails(
            from_addr=test_user,
            include_recalled=False,
            limit=5
        )
        print(f"✅ list_sent_emails支持include_recalled参数，返回{len(sent_emails)}封邮件")
    except Exception as e:
        print(f"❌ list_sent_emails不支持include_recalled参数: {e}")
        return False
    
    # 3. 测试撤回功能
    print("\n3️⃣ 测试撤回功能...")
    try:
        # 查找一个可撤回的邮件
        recallable = service.get_recallable_emails(test_user, limit=1)
        if recallable:
            print(f"✅ 找到{len(recallable)}封可撤回邮件")
        else:
            print("ℹ️ 没有找到可撤回邮件（正常情况）")
    except Exception as e:
        print(f"❌ 撤回功能测试失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！邮件撤回功能修复成功！")
    return True

if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)
