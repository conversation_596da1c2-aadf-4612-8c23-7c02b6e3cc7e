#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3
import sys

def check_database_schema():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构")
    print("=" * 50)
    
    try:
        db_path = "data/email_db.sqlite"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查emails表结构
        print("1️⃣ emails表结构:")
        cursor.execute("PRAGMA table_info(emails)")
        emails_columns = cursor.fetchall()
        
        has_is_recalled = False
        has_recalled_at = False
        has_recalled_by = False
        
        for col in emails_columns:
            col_id, name, type_, not_null, default, pk = col
            print(f"  - {name}: {type_} (默认: {default})")
            if name == "is_recalled":
                has_is_recalled = True
            elif name == "recalled_at":
                has_recalled_at = True
            elif name == "recalled_by":
                has_recalled_by = True
        
        print(f"\n📊 emails表撤回字段检查:")
        print(f"  - is_recalled: {'✅ 存在' if has_is_recalled else '❌ 缺失'}")
        print(f"  - recalled_at: {'✅ 存在' if has_recalled_at else '❌ 缺失'}")
        print(f"  - recalled_by: {'✅ 存在' if has_recalled_by else '❌ 缺失'}")
        
        # 2. 检查sent_emails表结构
        print("\n2️⃣ sent_emails表结构:")
        cursor.execute("PRAGMA table_info(sent_emails)")
        sent_emails_columns = cursor.fetchall()
        
        sent_has_is_recalled = False
        sent_has_recalled_at = False
        sent_has_recalled_by = False
        
        for col in sent_emails_columns:
            col_id, name, type_, not_null, default, pk = col
            print(f"  - {name}: {type_} (默认: {default})")
            if name == "is_recalled":
                sent_has_is_recalled = True
            elif name == "recalled_at":
                sent_has_recalled_at = True
            elif name == "recalled_by":
                sent_has_recalled_by = True
        
        print(f"\n📊 sent_emails表撤回字段检查:")
        print(f"  - is_recalled: {'✅ 存在' if sent_has_is_recalled else '❌ 缺失'}")
        print(f"  - recalled_at: {'✅ 存在' if sent_has_recalled_at else '❌ 缺失'}")
        print(f"  - recalled_by: {'✅ 存在' if sent_has_recalled_by else '❌ 缺失'}")
        
        # 3. 总结
        print("\n3️⃣ 问题分析:")
        missing_fields = []
        
        if not has_is_recalled:
            missing_fields.append("emails.is_recalled")
        if not has_recalled_at:
            missing_fields.append("emails.recalled_at")
        if not has_recalled_by:
            missing_fields.append("emails.recalled_by")
        if not sent_has_is_recalled:
            missing_fields.append("sent_emails.is_recalled")
        if not sent_has_recalled_at:
            missing_fields.append("sent_emails.recalled_at")
        if not sent_has_recalled_by:
            missing_fields.append("sent_emails.recalled_by")
        
        if missing_fields:
            print("❌ 数据库表缺少撤回功能所需字段:")
            for field in missing_fields:
                print(f"  - {field}")
            print("\n💡 需要执行数据库迁移来添加这些字段")
            return False
        else:
            print("✅ 数据库表结构完整，包含所有撤回功能字段")
            return True
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库表结构时出错: {e}")
        return False

def fix_database_schema():
    """修复数据库表结构"""
    print("\n🔧 修复数据库表结构")
    print("=" * 50)
    
    try:
        db_path = "data/email_db.sqlite"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 添加撤回功能字段
        alter_statements = [
            "ALTER TABLE emails ADD COLUMN is_recalled INTEGER DEFAULT 0",
            "ALTER TABLE emails ADD COLUMN recalled_at TEXT",
            "ALTER TABLE emails ADD COLUMN recalled_by TEXT",
            "ALTER TABLE sent_emails ADD COLUMN is_recalled INTEGER DEFAULT 0",
            "ALTER TABLE sent_emails ADD COLUMN recalled_at TEXT",
            "ALTER TABLE sent_emails ADD COLUMN recalled_by TEXT",
        ]
        
        for statement in alter_statements:
            try:
                cursor.execute(statement)
                print(f"✅ 执行成功: {statement}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    print(f"ℹ️ 字段已存在: {statement}")
                else:
                    print(f"❌ 执行失败: {statement} - {e}")
                    return False
        
        conn.commit()
        conn.close()
        
        print("\n✅ 数据库表结构修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复数据库表结构时出错: {e}")
        return False

def main():
    """主函数"""
    # 检查表结构
    schema_ok = check_database_schema()
    
    if not schema_ok:
        # 尝试修复
        print("\n" + "=" * 50)
        fix_ok = fix_database_schema()
        
        if fix_ok:
            # 重新检查
            print("\n" + "=" * 50)
            check_database_schema()
        else:
            print("❌ 数据库表结构修复失败")
            sys.exit(1)
    
    print("\n🎉 数据库表结构检查完成！")

if __name__ == "__main__":
    main()
