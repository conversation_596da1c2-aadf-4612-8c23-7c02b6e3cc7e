<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}邮件客户端{% endblock %}</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }
      .container {
        max-width: 1400px;
        padding: 0 20px;
      }
      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        margin-bottom: 25px;
      }
      .card-body {
        padding: 2rem;
      }
      .navbar-brand {
        font-weight: bold;
        color: #fff !important;
        font-size: 1.3rem;
      }
      .navbar {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px);
        border-radius: 15px;
        margin-bottom: 2rem;
        padding: 1rem 1.5rem;
      }
      .navbar-nav .nav-link {
        font-size: 1.1rem;
        padding: 0.8rem 1.2rem !important;
      }
      .btn {
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        font-weight: 500;
      }
      .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
      }
      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      }
      .alert {
        border-radius: 15px;
        border: none;
        padding: 1rem 1.5rem;
        font-size: 1rem;
      }
      .form-control,
      .form-select {
        border-radius: 10px;
        border: 2px solid #e0e0e0;
        padding: 0.8rem 1rem;
        font-size: 1rem;
      }
      .form-control:focus,
      .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
      }
      .form-label {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.75rem;
        color: #333;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        color: #333;
        font-weight: 600;
      }
      .table {
        font-size: 1rem;
      }
      .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        padding: 1rem;
      }
      .table td {
        padding: 1rem;
        vertical-align: middle;
      }
      .modal-content {
        border-radius: 15px;
        border: none;
      }
      .modal-header {
        border-bottom: 2px solid #f0f0f0;
        padding: 1.5rem;
      }
      .modal-body {
        padding: 1.5rem;
        font-size: 1rem;
      }
      .modal-footer {
        border-top: 2px solid #f0f0f0;
        padding: 1.5rem;
      }
    </style>
  </head>
  <body>
    <div class="container mt-4">
      {% if session.email %}
      <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
          <a class="navbar-brand" href="{{ url_for('index') }}">
            <i class="fas fa-envelope"></i> 邮件客户端
          </a>
          <div class="navbar-nav ms-auto">
            <a class="nav-link" href="{{ url_for('index') }}">
              <i class="fas fa-home"></i> 首页
            </a>
            <a class="nav-link" href="{{ url_for('send_email_page') }}">
              <i class="fas fa-paper-plane"></i> 发送邮件
            </a>
            <a class="nav-link" href="{{ url_for('receive_emails_page') }}">
              <i class="fas fa-inbox"></i> 接收邮件
            </a>
            <a class="nav-link" href="{{ url_for('logout') }}">
              <i class="fas fa-sign-out-alt"></i> 退出
            </a>
          </div>
        </div>
      </nav>
      {% endif %}

      <!-- Flash消息 -->
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %} {% for category, message in messages %}
      <div
        class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
        role="alert"
      >
        <i
          class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }}"
        ></i>
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
        ></button>
      </div>
      {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
  </body>
</html>
