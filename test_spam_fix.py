#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
import datetime
sys.path.append('.')

def test_spam_fix():
    """测试垃圾过滤修复"""
    print("=== 测试垃圾过滤修复 ===\n")
    
    try:
        # 1. 直接查看数据库中的邮件状态
        print("1. 检查数据库中的邮件状态:")
        conn = sqlite3.connect("data/email_db.sqlite")
        cursor = conn.cursor()
        
        cursor.execute("SELECT message_id, subject, is_spam, spam_score FROM emails ORDER BY rowid DESC LIMIT 5")
        emails = cursor.fetchall()
        
        for msg_id, subject, is_spam, spam_score in emails:
            status = "垃圾" if is_spam else "正常"
            print(f"   - {subject[:40]}... [{status}] 评分:{spam_score}")
        
        conn.close()
        
        # 2. 测试垃圾过滤器
        print("\n2. 测试垃圾过滤器:")
        from spam_filter.spam_filter import KeywordSpamFilter
        
        spam_filter = KeywordSpamFilter()
        
        # 测试明显的垃圾邮件
        test_email = {
            "from_addr": "<EMAIL>",
            "subject": "奖金发放通知",
            "content": "恭喜您中奖了，请点击领取您的奖金！"
        }
        
        result = spam_filter.analyze_email(test_email)
        print(f"   测试邮件: {test_email['subject']}")
        print(f"   判定结果: {'垃圾邮件' if result['is_spam'] else '正常邮件'}")
        print(f"   垃圾评分: {result['score']:.1f}")
        print(f"   匹配关键词: {result['matched_keywords']}")
        
        # 3. 测试数据库过滤功能
        print("\n3. 测试数据库过滤功能:")
        from server.new_db_handler import DatabaseHandler
        
        db_handler = DatabaseHandler()
        
        # 测试仅显示正常邮件
        normal_emails = db_handler.list_emails(include_spam=False, is_spam=False, limit=10)
        print(f"   仅正常邮件查询: {len(normal_emails)} 封")
        
        # 检查结果中是否有垃圾邮件
        spam_in_normal = [email for email in normal_emails if email.get('is_spam', False)]
        if spam_in_normal:
            print(f"   ❌ 正常邮件查询中包含 {len(spam_in_normal)} 封垃圾邮件")
            for email in spam_in_normal:
                print(f"      - {email.get('subject', '')[:30]}...")
        else:
            print(f"   ✅ 正常邮件查询结果正确")
        
        # 测试仅显示垃圾邮件
        spam_emails = db_handler.list_emails(include_spam=True, is_spam=True, limit=10)
        print(f"   仅垃圾邮件查询: {len(spam_emails)} 封")
        
        # 检查结果中是否有正常邮件
        normal_in_spam = [email for email in spam_emails if not email.get('is_spam', True)]
        if normal_in_spam:
            print(f"   ❌ 垃圾邮件查询中包含 {len(normal_in_spam)} 封正常邮件")
            for email in normal_in_spam:
                print(f"      - {email.get('subject', '')[:30]}...")
        else:
            print(f"   ✅ 垃圾邮件查询结果正确")
        
        # 4. 手动更新一封明显的垃圾邮件
        print("\n4. 手动更新垃圾邮件状态:")
        
        # 查找包含垃圾关键词的邮件
        conn = sqlite3.connect("data/email_db.sqlite")
        cursor = conn.cursor()
        
        cursor.execute("SELECT message_id, subject, is_spam FROM emails WHERE subject LIKE '%奖金发放%' LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            msg_id, subject, current_is_spam = result
            print(f"   找到邮件: {subject}")
            print(f"   当前状态: {'垃圾' if current_is_spam else '正常'}")
            
            # 如果不是垃圾邮件，手动更新为垃圾邮件
            if not current_is_spam:
                cursor.execute("UPDATE emails SET is_spam = 1, spam_score = 5.0 WHERE message_id = ?", (msg_id,))
                conn.commit()
                print(f"   ✅ 已更新为垃圾邮件")
            else:
                print(f"   ✅ 已经是垃圾邮件")
        else:
            print(f"   没有找到包含'奖金发放'的邮件")
        
        conn.close()
        
        # 5. 再次测试过滤功能
        print("\n5. 再次测试过滤功能:")
        
        # 重新测试
        normal_emails_after = db_handler.list_emails(include_spam=False, is_spam=False, limit=10)
        spam_emails_after = db_handler.list_emails(include_spam=True, is_spam=True, limit=10)
        
        print(f"   更新后正常邮件: {len(normal_emails_after)} 封")
        print(f"   更新后垃圾邮件: {len(spam_emails_after)} 封")
        
        # 显示垃圾邮件列表
        if spam_emails_after:
            print(f"   垃圾邮件列表:")
            for email in spam_emails_after:
                print(f"      - {email.get('subject', '')[:40]}... [评分:{email.get('spam_score', 0):.1f}]")
        
        print("\n=== 测试完成 ===")
        print("\n💡 现在你可以在CLI中测试:")
        print("   1. 运行 python cli.py")
        print("   2. 选择 '收件箱'")
        print("   3. 选择 '2. 仅显示正常邮件' 或 '3. 仅显示垃圾邮件'")
        print("   4. 检查过滤结果是否正确")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_spam_fix()
