#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

def test_recall():
    print("🔧 简单撤回测试")
    
    try:
        from server.email_repository import EmailRepository
        from server.db_connection import DatabaseConnection
        
        # 初始化
        db = DatabaseConnection("data/email_db.sqlite")
        repo = EmailRepository(db)
        
        # 测试撤回功能
        test_message_id = "test-message-123"
        user_email = "<EMAIL>"
        
        print(f"测试撤回邮件: {test_message_id}")
        
        # 直接调用撤回方法
        success = repo.recall_email(test_message_id, user_email)
        
        if success:
            print("✅ 撤回方法调用成功")
        else:
            print("❌ 撤回方法调用失败")
        
        print("测试完成")
        
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_recall()
