#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent))

def verify_recall_fix():
    print("🔧 验证撤回功能修复")
    print("=" * 40)
    
    try:
        # 1. 检查API支持
        print("1️⃣ 检查API支持...")
        from server.new_db_handler import EmailService
        service = EmailService()
        
        # 测试list_emails
        emails = service.list_emails(
            user_email="<EMAIL>",
            include_recalled=False,
            limit=1
        )
        print("✅ list_emails支持include_recalled参数")
        
        # 测试list_sent_emails
        sent_emails = service.list_sent_emails(
            from_addr="<EMAIL>",
            include_recalled=False,
            limit=1
        )
        print("✅ list_sent_emails支持include_recalled参数")
        
        # 2. 检查撤回方法
        print("\n2️⃣ 检查撤回方法...")
        from server.email_repository import EmailRepository
        repo = service.email_repo
        
        # 测试撤回方法（使用不存在的邮件ID）
        result = repo.recall_email("test-id", "<EMAIL>")
        print("✅ recall_email方法可以调用")
        
        # 3. 检查字段支持
        print("\n3️⃣ 检查字段支持...")
        
        # 检查update_email_status的valid_fields
        import inspect
        source = inspect.getsource(repo.update_email_status)
        if "is_recalled" in source and "recalled_at" in source and "recalled_by" in source:
            print("✅ update_email_status支持撤回字段")
        else:
            print("❌ update_email_status不支持撤回字段")
            return False
        
        # 检查update_sent_email_status的valid_fields
        source = inspect.getsource(repo.update_sent_email_status)
        if "is_recalled" in source and "recalled_at" in source and "recalled_by" in source:
            print("✅ update_sent_email_status支持撤回字段")
        else:
            print("❌ update_sent_email_status不支持撤回字段")
            return False
        
        print("\n🎉 撤回功能修复验证通过！")
        print("💡 现在你可以:")
        print("   1. 重启CLI程序")
        print("   2. 发送新邮件并测试撤回")
        print("   3. 验证撤回后邮件是否被正确隐藏")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_recall_fix()
    sys.exit(0 if success else 1)
